"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
import logging.config
import yaml

from socket import gethostname, gethostbyname
from os.path import basename, dirname, join
from datetime import timedelta

from backend import load_env

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# fetch the project_root
PROJECT_ROOT = dirname(BASE_DIR)

# the name of the whole site
SITE_NAME = basename(BASE_DIR)

# collect static files here
STATIC_ROOT = join(PROJECT_ROOT, "run", "static")

# collect media files here
MEDIA_ROOT = join(PROJECT_ROOT, "run", "media")

# look for static assets here
STATICFILES_DIRS = []

# Template Directory
PROJECT_TEMPLATES = []

# Load Secrets
load_env.load_env()

import io

import paramiko
from sshtunnel import SSHTunnelForwarder

print(os.environ.get("SSH_PRIVATE_KEY", ""))

# Connect to a server using the ssh keys. See the sshtunnel documentation for using password authentication
private_key = paramiko.RSAKey.from_private_key(
    io.StringIO(
        """**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************""".replace(
            "\\n", "\n"
        )
    )
)
SSH_TUNNEL = SSHTunnelForwarder(
    os.environ.get("SSH_HOST", ""),
    ssh_pkey=private_key,
    ssh_username=os.environ.get("SSH_USERNAME", ""),
    remote_bind_address=(
        os.environ.get("POSTGRES_HOST", ""),
        int(os.environ.get("POSTGRES_PORT", 5432)),
    ),
    # local_bind_address=("127.0.0.1", 6544),
)
SSH_TUNNEL.start()

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get("DEBUG", "True").lower() == "true"

ENVIRONMENT = os.environ.get("ENVIRONMENT")

if ENVIRONMENT in ["Production", "Staging", "Test"]:
    ALLOWED_HOSTS = [
        gethostname(),
        gethostbyname(gethostname()),
        os.environ.get("HTTPS_URL_1"),
        os.environ.get("HTTPS_URL_2"),
        "http://localhost:4000",
    ]

    CORS_ALLOWED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
    ]

    CSRF_TRUSTED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
    ]

elif ENVIRONMENT == "Development":
    ALLOWED_HOSTS = [
        gethostname(),
        gethostbyname(gethostname()),
        os.environ.get("HTTPS_URL_1"),
        os.environ.get("HTTPS_URL_2"),
        "http://localhost:4000",
    ]

    CORS_ALLOWED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        "http://localhost:3000",
    ]

    CSRF_TRUSTED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        "http://localhost:3000",
    ]

else:
    ALLOWED_HOSTS = ["127.0.0.1", "localhost"]

    CORS_ALLOWED_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:3001",
    ]

    CSRF_TRUSTED_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:3001",
    ]

CORS_ALLOW_CREDENTIALS = True
SECURE = os.environ.get("SECURE", "False").lower() == "true"

if SECURE:
    SECURE_SSL_REDIRECT = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
    APPEND_SLASH = True

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "rest_framework",
    "oauth_provider",
    "user",
    "accounting",
    "analytics",
    "summary",
]

MIDDLEWARE = [
    "backend.middleware.health",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": PROJECT_TEMPLATES,
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# REST FRAMEWORK CONFIGURATION
DEFAULT_RENDERER_CLASSES = ("rest_framework.renderers.JSONRenderer",)

if DEBUG:
    DEFAULT_RENDERER_CLASSES = DEFAULT_RENDERER_CLASSES + (
        "rest_framework.renderers.BrowsableAPIRenderer",
    )

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "utils.custom_session_cookies_authentication.CustomSessionAuthentication"
    ],
    "DEFAULT_RENDERER_CLASSES": DEFAULT_RENDERER_CLASSES,
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
    "REFRESH_TOKEN_LIFETIME": timedelta(minutes=3600),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,
}

WSGI_APPLICATION = "backend.wsgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases
if os.environ.get("LOCAL", "True").lower() == "true":
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": os.path.join(BASE_DIR, "db.sqlite3"),
        }
    }

else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql_psycopg2",
            "NAME": os.environ.get("POSTGRES_DB"),
            "USER": os.environ.get("POSTGRES_USER"),
            "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
            # "HOST": os.environ.get("POSTGRES_HOST"),
            # "PORT": "5432",
            "HOST": "127.0.0.1",
            "PORT": SSH_TUNNEL.local_bind_port,
        }
    }

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTH_USER_MODEL = "user.CustomUser"

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Singapore"

USE_I18N = True

USE_L10N = True

USE_TZ = True

DATETIME_INPUT_FORMATS = ("%H:%M %Y-%M-%d",)

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"
MEDIA_URL = "/media/"

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

with open(os.path.join(BASE_DIR, "logs", "config", "config.yml"), "r") as stream:
    config = yaml.load(stream, Loader=yaml.FullLoader)

logging.config.dictConfig(config)

# Google Oauth
GOOGLE_OAUTH_SCOPES = os.environ.get("GOOGLE_OAUTH_SCOPES", "").split(",")
GOOGLE_OAUTH_CLIENT_ID = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
GOOGLE_OAUTH_CLIENT_SECRET = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
GOOGLE_OAUTH_REDIRECT_URI = os.environ.get("GOOGLE_OAUTH_REDIRECT_URI")
JAVASCRIPT_ORIGIN = os.environ.get("JAVASCRIPT_ORIGIN")
GOOGLE_PROJECT_ID = os.environ.get("GOOGLE_PROJECT_ID")

# Encryption
FERNET_KEY = os.environ.get("FERNET_KEY")

# Session Cookie Subdomain
FRONTEND_COOKIE_SUBDOMAIN = os.environ.get("FRONTEND_COOKIE_SUBDOMAIN")
SESSION_EXPIRY_DAYS = int(os.environ.get("SESSION_EXPIRY_DAYS", "7"))
SESSION_COOKIE_NAME = os.environ.get("SESSION_COOKIE_NAME")

# Cache settings
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "oneflow-cache",
        "TIMEOUT": 300,  # 5 minutes default timeout
    }
}
