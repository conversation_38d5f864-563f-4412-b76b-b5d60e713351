from rest_framework.routers import DefaultRouter
from summary.views import (
    ProfitAndLossViewSet,
    CreditAndBalanceSheetViewSet,
    OperationsAndHRViewSet,
)

router = DefaultRouter()
router.register(r"profit-and-loss", ProfitAndLossViewSet, basename="profit-and-loss")
router.register(
    r"credit-and-balance-sheet",
    CreditAndBalanceSheetViewSet,
    basename="credit-and-balance-sheet",
)
router.register(
    r"operations-and-hr", OperationsAndHRViewSet, basename="operations-and-hr"
)

urlpatterns = router.urls
