from rest_framework.viewsets import ModelViewSet
from datetime import datetime
from collections import defaultdict, OrderedDict
from django.db.models import Decimal<PERSON>ield, Sum, Value, Q
from django.db.models.functions import Coalesce
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from rest_framework.decorators import action
from rest_framework import status
from fuzzywuzzy import fuzz, process
import re
import traceback
from accounting.models import ChartofAccount, JournalEntryTransaction, Clinic, Employee
from utils.response_template import custom_error_response, custom_success_response

# Cache timeout in seconds (24 hours)
CACHE_TTL = 60 * 60 * 24


class ProfitAndLossViewSet(ModelViewSet):
    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years + 1
            end_year = year

            def calculate_year_metrics(calc_year):
                start_date = datetime(calc_year, 1, 1)
                end_date = datetime(calc_year, 12, 31)

                # Helper to compute totals by account nature
                def get_total(accounts, is_credit_nature):
                    if not accounts:
                        return 0
                    entries = JournalEntryTransaction.objects.filter(
                        chart_of_account__id__in=accounts,
                        transaction_date__range=[start_date, end_date],
                    ).exclude(memo__icontains="FOR CLOSING")

                    data = entries.aggregate(
                        total_credit=Coalesce(
                            Sum("reporting_credit_amount"),
                            Value(0),
                            output_field=DecimalField(),
                        ),
                        total_debit=Coalesce(
                            Sum("reporting_debit_amount"),
                            Value(0),
                            output_field=DecimalField(),
                        ),
                    )

                    if is_credit_nature:
                        return data["total_credit"] - data["total_debit"]
                    else:
                        return data["total_debit"] - data["total_credit"]

                # 1. Gross Revenue
                revenue_accounts = ChartofAccount.objects.filter(
                    account_type__in=["Revenues", "Income"],
                    entity__currency=currency,
                ).values_list("id", flat=True)

                total_revenue = get_total(revenue_accounts, is_credit_nature=True)

                # 2. Cost of Sales
                cost_of_sales_accounts = ChartofAccount.objects.filter(
                    account_type="Cost of sales",
                    entity__currency=currency,
                ).values_list("id", flat=True)

                total_cost_of_sales = get_total(
                    cost_of_sales_accounts, is_credit_nature=False
                )

                # 3. EBITDA (Revenue - Cost of Sales - Operating Expenses)
                expense_accounts = (
                    ChartofAccount.objects.filter(
                        account_type="Expenses",
                        entity__currency=currency,
                    )
                    .exclude(
                        Q(account_name__icontains="depreciation")
                        | Q(account_name__icontains="amortization")
                        | Q(account_name__icontains="interest")
                        | Q(account_name__icontains="management fee")
                    )
                    .values_list("id", flat=True)
                )

                total_expenses = get_total(expense_accounts, is_credit_nature=False)
                ebitda = total_revenue - total_cost_of_sales - total_expenses

                # 4. Net Profit (Revenue - All Expenses)
                all_expense_accounts = ChartofAccount.objects.filter(
                    account_type="Expenses",
                    entity__currency=currency,
                ).values_list("id", flat=True)

                all_expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=all_expense_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                all_expense_data = all_expense_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                all_expenses = (
                    all_expense_data["total_debit"] - all_expense_data["total_credit"]
                )
                net_profit = total_revenue - all_expenses

                # 5. Bank Debt
                bank_debt_accounts = (
                    ChartofAccount.objects.filter(
                        account_type="Liability",
                        entity__currency=currency,
                    )
                    .filter(
                        Q(account_name__icontains="bank")
                        | Q(account_name__icontains="debt")
                    )
                    .values_list("id", flat=True)
                )

                bank_debt_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=bank_debt_accounts,
                    transaction_date__lte=end_date,
                ).exclude(memo__icontains="FOR CLOSING")

                bank_debt_data = bank_debt_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                bank_debt = (
                    bank_debt_data["total_credit"] - bank_debt_data["total_debit"]
                )

                # 6. Cash Balance
                cash_accounts = ChartofAccount.objects.filter(
                    cash_account=True,
                    entity__currency=currency,
                ).values_list("id", flat=True)

                cash_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=cash_accounts,
                    transaction_date__lte=end_date,
                ).exclude(memo__icontains="FOR CLOSING")

                cash_data = cash_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                cash_balance = cash_data["total_debit"] - cash_data["total_credit"]

                # 7. FTE (Full-Time Equivalent)
                FTE_WEIGHTS = {
                    "Permanent Full Time": 1.0,
                    "Permanent Part Time": 0.5,
                    "Casual Workers": 0.3,
                    "Intern": 0.2,
                    "Visting Consultant": 0.4,
                    "Contract Basis": 0.5,
                }

                employees = Employee.objects.filter(
                    Q(joined_date__year__lte=calc_year)
                    & (
                        Q(resignation_date__isnull=True)
                        | Q(resignation_date__year__gt=calc_year)
                    )
                )

                total_fte = 0
                for emp in employees:
                    emp_type = getattr(emp, "category", None)
                    fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                    total_fte += fte_value

                return {
                    "gross_revenue": float(total_revenue),
                    "ebitda": float(ebitda),
                    "net_profit": float(net_profit),
                    "bank_debt": float(bank_debt),
                    "cash_balance": float(cash_balance),
                    "fte": total_fte,
                }

            # Historical data for all years
            historical_data = {}
            for calc_year in range(start_year, end_year + 1):
                historical_data[calc_year] = calculate_year_metrics(calc_year)

            current_data = historical_data[year]
            prev_data = historical_data.get(year - 1, {})

            def calc_percentage_change(current, previous):
                if previous and previous != 0:
                    return float((current - previous) / previous * 100)
                return 0.0

            # Percentage change calculations
            gross_revenue_change = calc_percentage_change(
                current_data["gross_revenue"], prev_data.get("gross_revenue", 0)
            )
            ebitda_change = calc_percentage_change(
                current_data["ebitda"], prev_data.get("ebitda", 0)
            )
            net_profit_change = calc_percentage_change(
                current_data["net_profit"], prev_data.get("net_profit", 0)
            )
            bank_debt_change = calc_percentage_change(
                current_data["bank_debt"], prev_data.get("bank_debt", 0)
            )
            cash_balance_change = calc_percentage_change(
                current_data["cash_balance"], prev_data.get("cash_balance", 0)
            )
            fte_change = calc_percentage_change(
                current_data["fte"], prev_data.get("fte", 0)
            )

            total_assets = current_data["cash_balance"]
            debt_ratio = (
                (current_data["bank_debt"] / total_assets * 100)
                if total_assets > 0
                else 0
            )
            cash_ratio = (
                (current_data["cash_balance"] / current_data["bank_debt"] * 100)
                if current_data["bank_debt"] > 0
                else 100
            )

            chart_years = list(range(start_year, end_year + 1))

            overview_data = {
                "gross_revenue": {
                    "value": round(current_data["gross_revenue"], 0),
                    "percentage": round(gross_revenue_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": round(historical_data[y]["gross_revenue"], 0),
                        }
                        for y in chart_years
                    ],
                },
                "ebitda": {
                    "value": round(current_data["ebitda"], 0),
                    "percentage": round(ebitda_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": round(historical_data[y]["ebitda"], 0),
                        }
                        for y in chart_years
                    ],
                },
                "bank_debt": {
                    "value": round(current_data["bank_debt"], 0),
                    "percentage": round(bank_debt_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": round(historical_data[y]["bank_debt"], 0),
                        }
                        for y in chart_years
                    ],
                    "additional_info": {
                        "label": "Debt Ratio",
                        "value": f"{debt_ratio:.1f}pts",
                    },
                },
                "cash_balance": {
                    "value": round(current_data["cash_balance"], 0),
                    "percentage": round(cash_balance_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": round(historical_data[y]["cash_balance"], 0),
                        }
                        for y in chart_years
                    ],
                    "additional_info": {
                        "label": "Cash Ratio",
                        "value": f"{cash_ratio:.1f}pts",
                    },
                },
                "fte": {
                    "value": round(current_data["fte"], 0),
                    "percentage": round(fte_change, 1),
                    "chart_data": [
                        {"name": str(y), "value": round(historical_data[y]["fte"], 0)}
                        for y in chart_years
                    ],
                },
                "net_profit": {
                    "value": round(current_data["net_profit"], 0),
                    "percentage": round(net_profit_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": round(historical_data[y]["net_profit"], 0),
                        }
                        for y in chart_years
                    ],
                },
            }

            return custom_success_response(overview_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="revenue/chart")
    def revenue_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            chart_of_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            yearly_data = []
            revenue_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=chart_of_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                revenue_data = journal_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                revenue_by_year[y] = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

            for y in range(start_year, end_year + 1):
                revenue = revenue_by_year[y]
                prev_revenue = revenue_by_year.get(y - 1)

                if prev_revenue and prev_revenue != 0:
                    percentage = float((revenue - prev_revenue) / prev_revenue * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(revenue, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ebitda/chart")
    def ebitda_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Account IDs
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            cost_of_sales_accounts = ChartofAccount.objects.filter(
                account_type="Cost of sales",
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = (
                ChartofAccount.objects.filter(
                    account_type="Expenses",
                    entity__currency=currency,
                )
                .exclude(
                    Q(account_name__icontains="depreciation")
                    | Q(account_name__icontains="amortization")
                    | Q(account_name__icontains="interest")
                    | Q(account_name__icontains="management fee")
                )
                .values_list("id", flat=True)
            )

            ebitda_by_year = {}
            yearly_data = []

            def get_total(accounts, is_credit_nature):
                if not accounts:
                    return 0
                entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                data = entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                if is_credit_nature:
                    return data["total_credit"] - data["total_debit"]
                else:
                    return data["total_debit"] - data["total_credit"]

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                total_revenue = get_total(revenue_accounts, is_credit_nature=True)
                total_cost_of_sales = get_total(
                    cost_of_sales_accounts, is_credit_nature=False
                )
                total_expenses = get_total(expense_accounts, is_credit_nature=False)

                ebitda = total_revenue - total_cost_of_sales - total_expenses
                ebitda_by_year[y] = ebitda

            for y in range(start_year, end_year + 1):
                ebitda = ebitda_by_year.get(y, 0)
                prev_ebitda = ebitda_by_year.get(y - 1)

                if prev_ebitda and prev_ebitda != 0:
                    percentage = float((ebitda - prev_ebitda) / prev_ebitda * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(ebitda, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="net-profit/chart")
    def net_profit_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Define account type groups - Updated to include all relevant account types
            revenue_account_types = ["Revenues", "Income", "Other Income"]
            expense_account_types = ["Expenses", "Expense", "Cost of sales"]

            # Get account IDs
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=revenue_account_types,
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=expense_account_types,
                entity__currency=currency,
            ).values_list("id", flat=True)

            net_profit_by_year = {}
            yearly_data = []

            def get_total(accounts, is_credit_nature):
                if not accounts:
                    return 0
                entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                data = entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                if is_credit_nature:
                    return data["total_credit"] - data["total_debit"]
                else:
                    return data["total_debit"] - data["total_credit"]

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                total_revenue = get_total(revenue_accounts, is_credit_nature=True)
                total_expenses = get_total(expense_accounts, is_credit_nature=False)

                net_profit = total_revenue - total_expenses
                net_profit_by_year[y] = net_profit

            for y in range(start_year, end_year + 1):
                net_profit = net_profit_by_year.get(y, 0)
                prev_net_profit = net_profit_by_year.get(y - 1)

                if prev_net_profit and prev_net_profit != 0:
                    percentage = float(
                        (net_profit - prev_net_profit) / prev_net_profit * 100
                    )
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(net_profit, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(
        detail=False, methods=["get"], url_path="revenue-breakdown/by-segment/chart"
    )
    def revenue_breakdown_by_segment_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            today = datetime.now()
            month_day = (today.month, today.day)

            chart_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values("id", "clinic_code")

            coa_id_to_clinic_code = {
                str(c["id"]): c["clinic_code"] for c in chart_accounts
            }
            coa_ids = list(coa_id_to_clinic_code.keys())

            clinic_code_to_segment = dict(Clinic.objects.values_list("code", "segment"))

            year_data = {}
            all_segments = set()

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, month_day[0], month_day[1])

                journal_entries = (
                    JournalEntryTransaction.objects.filter(
                        chart_of_account__id__in=coa_ids,
                        transaction_date__range=[start_date, end_date],
                    )
                    .exclude(memo__icontains="FOR CLOSING")
                    .select_related("chart_of_account")
                )

                revenue_by_segment = defaultdict(float)

                for entry in journal_entries:
                    coa_id = str(entry.chart_of_account_id)
                    clinic_code = coa_id_to_clinic_code.get(coa_id)
                    segment = clinic_code_to_segment.get(clinic_code, "Others")

                    credit = float(entry.reporting_credit_amount or 0)
                    debit = float(entry.reporting_debit_amount or 0)
                    amount = credit - debit
                    revenue_by_segment[segment] += amount

                all_segments.update(revenue_by_segment.keys())

                sorted_segments = sorted(
                    [s for s in revenue_by_segment if s != "Others"]
                )
                if "Others" in revenue_by_segment:
                    sorted_segments.append("Others")

                total = sum(revenue_by_segment.values())

                record = {
                    "name": f"YTD<br/>{y}",
                    "total": round(total, 2),
                }

                for seg in sorted_segments:
                    record[seg] = round(revenue_by_segment.get(seg, 0.0), 2)

                year_data[y] = record

            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            formatted_data = []
            for y in range(start_year, end_year + 1):
                record = year_data[y]
                for seg in final_segments:
                    if seg not in record:
                        record[seg] = 0.0

                ordered_record = OrderedDict(
                    [("name", record["name"]), ("total", record["total"])]
                    + [(seg, record[seg]) for seg in final_segments]
                )
                formatted_data.append(ordered_record)

            return custom_success_response(formatted_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class CreditAndBalanceSheetViewSet(ModelViewSet):
    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="total-debt/chart")
    def total_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get relevant total debt accounts
            debt_accounts = ChartofAccount.objects.filter(
                total_debt=True, entity__currency=currency
            ).values_list("id", flat=True)

            debt_by_year = {}

            for y in range(
                start_year - 1, end_year + 1
            ):  # include previous year for % calc
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                debt_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=debt_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                totals = debt_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_debt = totals["total_credit"] - totals["total_debit"]
                debt_by_year[y] = total_debt

            # Build final response list
            response = []
            for y in range(start_year, end_year + 1):
                current = debt_by_year.get(y, 0)
                previous = debt_by_year.get(y - 1, 0)

                if previous != 0:
                    percentage = float((current - previous) / previous * 100)
                else:
                    percentage = 0.0

                response.append(
                    {
                        "name": str(y),
                        "amount": float(round(current, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="bank-and-net-debt/chart")
    def bank_and_net_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 3))

            start_year = year - last_n_years
            end_year = year

            # Filter account sets
            bank_debt_accounts = ChartofAccount.objects.filter(
                bank_debt=True, entity__currency=currency
            ).values_list("id", flat=True)

            net_debt_accounts = ChartofAccount.objects.filter(
                total_debt=True, entity__currency=currency
            ).values_list("id", flat=True)

            response = []

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                # Bank Debt
                bank_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=bank_debt_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                bank_totals = bank_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                bank_debt = bank_totals["total_credit"] - bank_totals["total_debit"]

                # Net Debt
                net_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=net_debt_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                net_totals = net_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                net_debt = net_totals["total_credit"] - net_totals["total_debit"]

                response.append(
                    {
                        "name": str(y),
                        "bankDebt": float(round(bank_debt, 2)),
                        "netDebt": float(round(net_debt, 2)),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="current-cash-balance/chart")
    def current_cash_balance_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            prev_year = year - 1

            def sum_amount(qs):
                return qs.aggregate(
                    debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

            def net_amount(qs):
                sums = sum_amount(qs)
                return float(sums["debit"] - sums["credit"])

            # Starting Cash
            starting_cash_accounts = ChartofAccount.objects.filter(
                account_type="Current Asset",
                cash_account=True,
                entity__currency=currency,
            ).values_list("id", flat=True)
            start_cash_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=starting_cash_accounts,
                transaction_date__year=prev_year,
            ).exclude(memo__icontains="FOR CLOSING")
            starting_cash = net_amount(start_cash_entries)

            # OCF
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Income", "Revenues"], entity__currency=currency
            ).values_list("id", flat=True)
            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=["Expenses", "Expense"], entity__currency=currency
            ).values_list("id", flat=True)
            revenue_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=revenue_accounts, transaction_date__year=year
            ).exclude(memo__icontains="FOR CLOSING")
            expense_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=expense_accounts, transaction_date__year=year
            ).exclude(memo__icontains="FOR CLOSING")
            ocf = net_amount(revenue_entries) - net_amount(expense_entries)

            # CAPEX
            capex_accounts = ChartofAccount.objects.filter(
                account_type__in=["Asset", "Assets", "Non-current Asset"],
                cash_account=False,
                entity__currency=currency,
            ).values_list("id", flat=True)
            capex_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=capex_accounts,
                transaction_date__year=year,
            ).exclude(memo__icontains="FOR CLOSING")
            capex = net_amount(capex_entries)

            # Share Loans
            share_loan_accounts = (
                ChartofAccount.objects.filter(
                    account_type="Equity",
                    entity__currency=currency,
                )
                .filter(
                    Q(account_name__icontains="share")
                    | Q(account_name__icontains="loan")
                )
                .values_list("id", flat=True)
            )
            share_loan_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=share_loan_accounts,
                transaction_date__year=year,
            ).exclude(memo__icontains="FOR CLOSING")
            share_loans = net_amount(share_loan_entries)

            # Bank Loans
            bank_loan_accounts = ChartofAccount.objects.filter(
                bank_debt=True,
                entity__currency=currency,
            ).values_list("id", flat=True)
            bank_loan_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=bank_loan_accounts, transaction_date__year=year
            ).exclude(memo__icontains="FOR CLOSING")
            bank_loans = net_amount(bank_loan_entries)

            # Dividends
            dividend_accounts = (
                ChartofAccount.objects.filter(
                    account_type="Equity",
                    entity__currency=currency,
                )
                .filter(
                    Q(account_name__icontains="dividend")
                    | Q(remark__icontains="dividend")
                )
                .values_list("id", flat=True)
            )
            dividend_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=dividend_accounts, transaction_date__year=year
            ).exclude(memo__icontains="FOR CLOSING")
            dividends = -abs(net_amount(dividend_entries))  # Ensure it's negative

            # Waterfall construction
            flows = [
                {
                    "name": str(prev_year),
                    "amount": round(starting_cash, 2),
                    "remaining": 0,
                },
                {"name": "OCF", "amount": round(ocf, 2)},
                {"name": "CAPEX", "amount": round(-capex, 2)},
                {"name": "Share<br/>Loans", "amount": round(share_loans, 2)},
                {"name": "Bank<br/>Loan", "amount": round(bank_loans, 2)},
                {"name": "Dividends", "amount": round(dividends, 2)},
            ]

            # Remaining calculation
            current = starting_cash
            for f in flows[1:]:
                f["remaining"] = round(current, 2)
                current += f["amount"]

            # Final year ending cash
            flows.append(
                {"name": str(year), "amount": round(current, 2), "remaining": 0}
            )

            return custom_success_response(flows)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ocf/chart")
    def ocf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get COA IDs relevant to operating activities
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=["Expenses", "Operating Expenses", "Cost of sales"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            yearly_data = []

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                # Income
                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                total_revenue = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                net_revenue = (
                    total_revenue["total_credit"] - total_revenue["total_debit"]
                )

                # Expenses
                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                total_expense = expense_entries.aggregate(
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                net_expense = (
                    total_expense["total_debit"] - total_expense["total_credit"]
                )

                # Approximate OCF = Net Revenue - Operating Expenses
                ocf = net_revenue - net_expense

                yearly_data.append(
                    {
                        "name": str(y),
                        "value": float(round(ocf, 2)),
                    }
                )

            return custom_success_response(yearly_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="fcf/chart")
    def fcf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Relevant COA groupings
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=["Expenses", "Operating Expenses", "Cost of sales"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            capex_accounts = ChartofAccount.objects.filter(
                account_type__in=["Non-current Asset", "Asset", "Assets"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            fcf_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                # Revenues
                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")
                revenue_data = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                net_revenue = revenue_data["total_credit"] - revenue_data["total_debit"]

                # Operating Expenses
                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")
                expense_data = expense_entries.aggregate(
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                net_expense = expense_data["total_debit"] - expense_data["total_credit"]

                ocf = net_revenue - net_expense

                # CapEx (asset purchases)
                capex_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=capex_accounts,
                    transaction_date__range=[start_date, end_date],
                ).exclude(memo__icontains="FOR CLOSING")
                capex_data = capex_entries.aggregate(
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
                capex = capex_data["total_debit"] - capex_data["total_credit"]

                fcf = ocf - capex

                fcf_by_year[y] = fcf

            # Construct final response
            response = []
            for y in range(start_year, end_year + 1):
                current = fcf_by_year[y]
                previous = fcf_by_year.get(y - 1)

                if previous and previous != 0:
                    percentage = float((current - previous) / previous * 100)
                else:
                    percentage = 0.0

                response.append(
                    {
                        "name": str(y),
                        "amount": float(round(current, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class OperationsAndHRViewSet(ModelViewSet):
    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 4))

            # Define resignation reason categories
            VOLUNTARY_REASONS = {
                "Back to School",
                "Better Benefits",
                "Career Progression",
                "End of Contract",
                "Family Reasons",
                "Fellow Colleagues",
                "Further Education",
                "Health Reasons",
                "Job Mismatch",
                "Remunerations",
                "Resigned",
                "Supervisor / Management",
            }

            INVOLUNTARY_REASONS = {
                "Company Direction",
                "Dismissed",
                "Not for Rehire",
                "Terminated",
            }

            # Calculate revenue per FTE
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)

            revenue_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=revenue_accounts,
                transaction_date__range=[start_date, end_date],
            ).exclude(memo__icontains="FOR CLOSING")

            revenue_data = revenue_entries.aggregate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )

            total_revenue = revenue_data["total_credit"] - revenue_data["total_debit"]

            # Calculate FTE count
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            employees = Employee.objects.filter(
                joined_date__year__lte=year, resignation_date__isnull=True
            )

            total_fte = 0
            total_employees = 0
            doctor_count = 0

            for emp in employees:
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                total_fte += fte_value
                total_employees += 1

                if emp.department == "Doctor":
                    doctor_count += 1

            # Calculate metrics
            revenue_per_fte = float(total_revenue) / total_fte if total_fte > 0 else 0
            revenue_per_doctor = (
                float(total_revenue) / doctor_count if doctor_count > 0 else 0
            )

            # Calculate previous year data for percentage changes
            prev_year = year - 1
            prev_start_date = datetime(prev_year, 1, 1)
            prev_end_date = datetime(prev_year, 12, 31)

            prev_revenue_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=revenue_accounts,
                transaction_date__range=[prev_start_date, prev_end_date],
            ).exclude(memo__icontains="FOR CLOSING")

            prev_revenue_data = prev_revenue_entries.aggregate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )

            prev_total_revenue = (
                prev_revenue_data["total_credit"] - prev_revenue_data["total_debit"]
            )

            prev_employees = Employee.objects.filter(
                Q(joined_date__year__lte=prev_year)
                & (
                    Q(resignation_date__isnull=True)
                    | Q(resignation_date__year__gt=prev_year)
                )
            )

            prev_total_fte = 0
            prev_total_employees = 0
            prev_doctor_count = 0

            for emp in prev_employees:
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                prev_total_fte += fte_value
                prev_total_employees += 1

                if emp.department == "Doctor":
                    prev_doctor_count += 1

            prev_revenue_per_fte = (
                float(prev_total_revenue) / prev_total_fte if prev_total_fte > 0 else 0
            )
            prev_revenue_per_doctor = (
                float(prev_total_revenue) / prev_doctor_count
                if prev_doctor_count > 0
                else 0
            )

            # Calculate previous year attrition rates
            prev_resigned_employees = Employee.objects.filter(
                resignation_date__year=prev_year, resignation_date__isnull=False
            )

            prev_voluntary_resignations = prev_resigned_employees.filter(
                resignation_reason__in=VOLUNTARY_REASONS
            ).count()

            prev_involuntary_resignations = prev_resigned_employees.filter(
                resignation_reason__in=INVOLUNTARY_REASONS
            ).count()

            prev_positive_attrition_rate = (
                (prev_voluntary_resignations / prev_total_employees * 100)
                if prev_total_employees > 0
                else 0
            )
            prev_negative_attrition_rate = (
                (prev_involuntary_resignations / prev_total_employees * 100)
                if prev_total_employees > 0
                else 0
            )

            # Calculate percentage changes
            revenue_per_fte_change = 0
            if prev_revenue_per_fte > 0:
                revenue_per_fte_change = (
                    (revenue_per_fte - prev_revenue_per_fte) / prev_revenue_per_fte
                ) * 100

            revenue_per_doctor_change = 0
            if prev_revenue_per_doctor > 0:
                revenue_per_doctor_change = (
                    (revenue_per_doctor - prev_revenue_per_doctor)
                    / prev_revenue_per_doctor
                ) * 100

            employees_change = 0
            if prev_total_employees > 0:
                employees_change = (
                    (total_employees - prev_total_employees) / prev_total_employees
                ) * 100

            # Calculate attrition rates based on resignation reasons
            # Positive attrition: voluntary departures
            # Negative attrition: involuntary departures

            resigned_employees = Employee.objects.filter(
                resignation_date__year=year, resignation_date__isnull=False
            )

            voluntary_resignations = resigned_employees.filter(
                resignation_reason__in=VOLUNTARY_REASONS
            ).count()

            involuntary_resignations = resigned_employees.filter(
                resignation_reason__in=INVOLUNTARY_REASONS
            ).count()

            positive_attrition_rate = (
                (voluntary_resignations / total_employees * 100)
                if total_employees > 0
                else 0
            )
            negative_attrition_rate = (
                (involuntary_resignations / total_employees * 100)
                if total_employees > 0
                else 0
            )

            # Calculate attrition rate percentage changes
            positive_attrition_change = 0
            if prev_positive_attrition_rate > 0:
                positive_attrition_change = (
                    (positive_attrition_rate - prev_positive_attrition_rate)
                    / prev_positive_attrition_rate
                ) * 100
            elif positive_attrition_rate > 0:
                positive_attrition_change = (
                    100  # New attrition where there was none before
                )

            negative_attrition_change = 0
            if prev_negative_attrition_rate > 0:
                negative_attrition_change = (
                    (negative_attrition_rate - prev_negative_attrition_rate)
                    / prev_negative_attrition_rate
                ) * 100
            elif negative_attrition_rate > 0:
                negative_attrition_change = (
                    100  # New attrition where there was none before
                )

            # Calculate historical data for charts (last N years)
            chart_years = list(range(year - last_n_years + 1, year + 1))
            historical_data = {}

            for chart_year in chart_years:
                # Revenue for this year
                chart_start_date = datetime(chart_year, 1, 1)
                chart_end_date = datetime(chart_year, 12, 31)

                chart_revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[chart_start_date, chart_end_date],
                ).exclude(memo__icontains="FOR CLOSING")

                chart_revenue_data = chart_revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                chart_total_revenue = (
                    chart_revenue_data["total_credit"]
                    - chart_revenue_data["total_debit"]
                )

                # Employees for this year
                chart_employees = Employee.objects.filter(
                    Q(joined_date__year__lte=chart_year)
                    & (
                        Q(resignation_date__isnull=True)
                        | Q(resignation_date__year__gt=chart_year)
                    )
                )

                chart_total_fte = 0
                chart_total_employees = 0
                chart_doctor_count = 0
                chart_resigned_employees = 0

                for emp in chart_employees:
                    emp_type = getattr(emp, "category", None)
                    fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                    chart_total_fte += fte_value
                    chart_total_employees += 1

                    if emp.department == "Doctor":
                        chart_doctor_count += 1

                # Count resignations for this year by category
                chart_resigned_employees = Employee.objects.filter(
                    resignation_date__year=chart_year, resignation_date__isnull=False
                )

                chart_voluntary_resignations = chart_resigned_employees.filter(
                    resignation_reason__in=VOLUNTARY_REASONS
                ).count()

                chart_involuntary_resignations = chart_resigned_employees.filter(
                    resignation_reason__in=INVOLUNTARY_REASONS
                ).count()

                # Calculate metrics for this year
                chart_revenue_per_fte = (
                    float(chart_total_revenue) / chart_total_fte
                    if chart_total_fte > 0
                    else 0
                )
                chart_revenue_per_doctor = (
                    float(chart_total_revenue) / chart_doctor_count
                    if chart_doctor_count > 0
                    else 0
                )
                chart_positive_attrition_rate = (
                    (chart_voluntary_resignations / chart_total_employees * 100)
                    if chart_total_employees > 0
                    else 0
                )
                chart_negative_attrition_rate = (
                    (chart_involuntary_resignations / chart_total_employees * 100)
                    if chart_total_employees > 0
                    else 0
                )

                historical_data[chart_year] = {
                    "revenue_per_fte": round(chart_revenue_per_fte, 0),
                    "revenue_per_doctor": round(chart_revenue_per_doctor, 0),
                    "employees": chart_total_employees,
                    "positive_attrition_rate": round(chart_positive_attrition_rate, 1),
                    "negative_attrition_rate": round(chart_negative_attrition_rate, 1),
                }

            overview_data = {
                "revenue_per_fte": {
                    "value": round(revenue_per_fte, 0),
                    "percentage": round(revenue_per_fte_change, 1),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["revenue_per_fte"]}
                        for y in chart_years
                    ],
                },
                "revenue_per_doctor": {
                    "value": round(revenue_per_doctor, 0),
                    "percentage": round(revenue_per_doctor_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["revenue_per_doctor"],
                        }
                        for y in chart_years
                    ],
                },
                "employees": {
                    "value": total_employees,
                    "percentage": round(employees_change, 1),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["employees"]}
                        for y in chart_years
                    ],
                },
                "positive_attrition_rate": {
                    "value": round(positive_attrition_rate, 1),
                    "percentage": round(positive_attrition_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["positive_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
                "negative_attrition_rate": {
                    "value": round(negative_attrition_rate, 1),
                    "percentage": round(negative_attrition_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["negative_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
            }

            return custom_success_response(overview_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="clinics/chart")
    def clinics_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get all relevant Chart of Accounts
            chart_of_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            # Build clinic_code → segment map
            clinics = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "code", "segment"
            )
            clinic_segment_map = {c["code"]: c["segment"] or "Others" for c in clinics}

            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                # Get distinct clinic_codes from JournalEntryTransactions
                active_clinic_codes = (
                    JournalEntryTransaction.objects.filter(
                        chart_of_account_id__in=chart_of_accounts,
                        transaction_date__range=(start_date, end_date),
                        chart_of_account__clinic_code__in=clinic_segment_map.keys(),
                    )
                    .exclude(memo__icontains="FOR CLOSING")
                    .values_list("chart_of_account__clinic_code", flat=True)
                    .distinct()
                )

                for code in active_clinic_codes:
                    segment = clinic_segment_map.get(code, "Others")
                    if segment == "Corporate":
                        continue
                    all_segments.add(segment)
                    chart_data[y][segment] += 1
                    chart_data[y]["total"] += 1

            # Segment ordering: alphabetical + 'Others' last
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Format the result
            result = []
            for y in range(start_year, end_year + 1):
                year_entry = {"name": str(y)}
                for segment in final_segments:
                    year_entry[segment] = chart_data[y].get(segment, 0)
                year_entry["total"] = chart_data[y].get("total", 0)
                result.append(year_entry)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="doctors/chart")
    def doctors_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get clinic names and segments
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_to_segment = {
                normalize_clinic_name(entry["name"]): entry["segment"]
                for entry in clinic_qs
            }

            # Get all relevant doctors
            employees = Employee.objects.filter(
                department="Doctor", joined_date__year__lte=end_year
            )

            # Prepare chart data
            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                clinic_name = emp.clinic_name
                segment = match_clinic_segment(clinic_name, normalized_to_segment)
                all_segments.add(segment)

                for y in range(start_year, end_year + 1):
                    if emp.joined_date.year <= y and (
                        not emp.resignation_date or emp.resignation_date.year > y
                    ):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Order segments with "Others" at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build formatted result
            result = []
            for y in sorted(chart_data.keys()):
                year_entry = {"name": str(y)}
                for segment in final_segments:
                    year_entry[segment] = chart_data[y].get(segment, 0)
                year_entry["total"] = chart_data[y]["total"]
                result.append(year_entry)

            return custom_success_response(result)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="fte/chart")
    def fte_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # FTE conversion weights
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            # Department to Category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            def get_category(dept):
                for category, departments in CATEGORY_MAP.items():
                    if dept in departments:
                        return category
                return "Other"

            # Get all employees who joined before or during the end year
            employees = Employee.objects.filter(joined_date__year__lte=end_year)

            # Prepare chart data
            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                department = getattr(emp, "department", "").strip()
                category = get_category(department)
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if (
                        joined_year
                        and joined_year <= y
                        and (not resigned_year or resigned_year > y)
                    ):
                        chart_data[y][category] += fte_value
                        chart_data[y]["total"] += fte_value

            # Format result for frontend
            result = []
            categories = list(CATEGORY_MAP.keys())

            for y in range(start_year, end_year + 1):
                year_entry = {"name": str(y)}
                for cat in categories:
                    year_entry[cat] = round(chart_data[y].get(cat, 0.0), 2)
                year_entry["total"] = round(chart_data[y].get("total", 0.0), 2)
                result.append(year_entry)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="headcount/chart")
    def headcount_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            type = (
                request.query_params.get("type", "full-time").replace("-", " ").lower()
            )

            start_year = year - last_n_years
            end_year = year

            # Get clinic names and segments
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_to_segment = {
                normalize_clinic_name(entry["name"]): entry["segment"]
                for entry in clinic_qs
            }

            # Dynamically filter categories based on 'type'
            if "part" in type:
                category_filter = "Part Time"
            else:
                category_filter = "Full Time"

            # Get all relevant doctors
            employees = Employee.objects.filter(
                category__icontains=category_filter, joined_date__year__lte=end_year
            )

            # Prepare chart data
            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                clinic_name = emp.clinic_name
                segment = match_clinic_segment(clinic_name, normalized_to_segment)
                all_segments.add(segment)

                for y in range(start_year, end_year + 1):
                    if emp.joined_date.year <= y and (
                        not emp.resignation_date or emp.resignation_date.year > y
                    ):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Order segments with "Others" at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build formatted result
            result = []
            for y in sorted(chart_data.keys()):
                year_entry = {"name": str(y)}
                for segment in final_segments:
                    year_entry[segment] = chart_data[y].get(segment, 0)
                year_entry["total"] = chart_data[y]["total"]
                result.append(year_entry)

            return custom_success_response(result)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="org-chart")
    def org_chart(self, request):
        try:
            employees = Employee.objects.filter(resignation_date__isnull=True)

            # Nested dict: clinic_name -> department_group -> occupation -> codes
            hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))

            department_mapping = {
                "Doctor": "Doctor",
                "Clinical": "Clinical",
                "Front Desk": "Front Desk",
            }

            for emp in employees:
                clinic = emp.clinic_name
                department = emp.department
                occupation = emp.occupation
                code = emp.code

                # Only include mapped departments
                if department in department_mapping:
                    hierarchy[clinic][department_mapping[department]][
                        occupation
                    ].append(code)

            def build_tree(name, children_dict):
                children = []
                for key, val in children_dict.items():
                    if isinstance(val, dict):
                        children.append(build_tree(key, val))
                    elif isinstance(val, list):
                        children.append(
                            {"name": key, "children": [{"name": c} for c in val]}
                        )
                return {"name": name, "children": children}

            org_chart = {
                "name": "SMG",
                "children": [
                    build_tree(clinic, dept_data)
                    for clinic, dept_data in hierarchy.items()
                ],
            }

            return custom_success_response(org_chart)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


CLINIC_SEGMENT_MAPPING = {
    "alpha healthcare international pte ltd": "Women's Health",
    "astra @ gleneagles": "Women's Health",
    "astra womens's specialists (jl) pte ltd": "Women's Health",
    "babies and children specialist clinic pte ltd": "Paediatrics",
    "children's clinic central pte ltd": "Paediatrics",
    "children's clinic international": "Paediatrics",
    "hsc cancer centre pte ltd": "Oncology",
    "kids clinic @ bishan pte ltd": "Paediatrics",
    "kids clinic @ punggol": "Paediatrics",
    "lifescan imaging @ farrer park": "Imaging",
    "lifescan imaging @ novena": "Imaging",
    "lifescan imaging @ paragon": "Imaging",
    "lifescan medical @ farrer park": "Others",
    "lifescan medical @ novena": "Others",
    "lifescan medical @ paragon": "Others",
    "lsc eye clinic": "Others",
    "smg aesthetic (downtown) pte ltd": "Aesthetics",
    "smg astra centre for women pte ltd": "Women's Health",
    "smg astra o&g pte ltd": "Women's Health",
    "smg heart centre pte ltd": "Others",
    "smg kids orthopaedic pte ltd": "Others",
    "smg o&g centre pte ltd": "Women's Health",
    "sw1 aesthetics": "Aesthetics",
    "sw1 plastic surgery pte ltd": "Aesthetics",
    "tck @ novena pte ltd": "Women's Health",
    "the breast clinic pte ltd": "Others",
    "the cancer centre pte ltd": "Oncology",
    "the dental studio @ bishan": "Others",
    "the dental studio @ oue": "Others",
    "the dental studio @ paragon": "Others",
    "the dental studio @ tai thong": "Others",
    "the obstetrics & gynaecology centre @ mt e orchard": "Women's Health",
    "the obstetrics & gynaecology centre @ novena": "Women's Health",
    "the women's specialist centre (hc) pte ltd": "Women's Health",
    "togc @ gleneagles pte ltd": "Women's Health",
    "vidaskin pte ltd": "Aesthetics",
    "wellness & gynaecology centre pte ltd": "Women's Health",
}


def get_segment_from_mapping(clinic_name):
    return CLINIC_SEGMENT_MAPPING.get(clinic_name.lower(), "Others")


def normalize_clinic_name(name: str) -> str:
    if not name:
        return ""

    name = name.lower()
    name = re.sub(r"[^\w\s]", " ", name)
    name = re.sub(r"\b(at|@)\b", " at ", name)
    name = re.sub(r"\s+", " ", name).strip()

    replacements = {
        "togc": "the obstetrics gynaecology centre",
        "smg": "",
        "centre": "center",
        "centres": "centers",
        "womens": "women's",
        "kid's": "kids",
        "tai thong": "tai tong",
    }
    for old, new in replacements.items():
        name = name.replace(old, new)

    return name.strip()


def match_clinic_segment(name, normalized_to_segment):
    # Step 1: Try exact match from hardcoded mapping
    mapped_segment = get_segment_from_mapping(name)
    if mapped_segment != "Others":
        return mapped_segment

    # Step 2: Fallback to normalized fuzzy DB match
    norm_name = normalize_clinic_name(name)
    match_result = process.extractOne(
        norm_name, normalized_to_segment.keys(), scorer=fuzz.token_sort_ratio
    )
    if match_result:
        match_name, score = match_result
        if score >= 70:
            return normalized_to_segment[match_name]

    return "Others"
