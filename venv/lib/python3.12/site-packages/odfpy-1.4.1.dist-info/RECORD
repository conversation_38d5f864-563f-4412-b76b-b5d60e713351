../../../bin/csv2ods,sha256=d1GiUrB62s0pOW-Fx52VtRou9qPajy_u__HwkcoMUxs,9094
../../../bin/mailodf,sha256=CgQs6thxgi1sMvUpFmcPx_BHVcQHxNtdG_UhYsNWXQ4,2854
../../../bin/odf2mht,sha256=bkrwNMJPuFSous_LyaRfo77QFD9LNALxvv8cMU62w8w,2332
../../../bin/odf2xhtml,sha256=rrF-j-9M0zgb5bsRz6_rjCx5Ba4Fh2J2HrIPrG8OwPI,1664
../../../bin/odf2xml,sha256=r8mpt9vt6EXioN66vvMchC2I2L5mNWKpelJf_ilv2Cs,2491
../../../bin/odfimgimport,sha256=n-qBcVi91KSSufLsJhJdlkPxh2nPptMex0AwgtA58Ns,6076
../../../bin/odflint,sha256=njAeSbFAmdSxAIBdRBMV-hE-yaxZHFoK5_fjZsQY6fI,7687
../../../bin/odfmeta,sha256=x-9SC4rmU4lXfMKh40bqdqdfP_6YNOkNRpmC8z1FB-o,8484
../../../bin/odfoutline,sha256=irxM3Ezrh_uuHE1yNzpS19qfsKD8wVLAcLjSdbxzzxI,4613
../../../bin/odfuserfield,sha256=mvLz18K9pcZIRGn34tgTj70jIqwimaRgGdsPWo7jxIo,2667
../../../bin/xml2odf,sha256=v_JIN20wDXCbXFfGs32g-HJrGgT8-O0fq6qMyulFF5U,8838
odf/__init__.py,sha256=8gHmlU2wJS6BOuMRnLMC9eyX_StLPN5lrEcr7T2PbfU,742
odf/__pycache__/__init__.cpython-312.pyc,,
odf/__pycache__/anim.cpython-312.pyc,,
odf/__pycache__/attrconverters.cpython-312.pyc,,
odf/__pycache__/chart.cpython-312.pyc,,
odf/__pycache__/config.cpython-312.pyc,,
odf/__pycache__/dc.cpython-312.pyc,,
odf/__pycache__/dr3d.cpython-312.pyc,,
odf/__pycache__/draw.cpython-312.pyc,,
odf/__pycache__/easyliststyle.cpython-312.pyc,,
odf/__pycache__/element.cpython-312.pyc,,
odf/__pycache__/elementtypes.cpython-312.pyc,,
odf/__pycache__/form.cpython-312.pyc,,
odf/__pycache__/grammar.cpython-312.pyc,,
odf/__pycache__/load.cpython-312.pyc,,
odf/__pycache__/manifest.cpython-312.pyc,,
odf/__pycache__/math.cpython-312.pyc,,
odf/__pycache__/meta.cpython-312.pyc,,
odf/__pycache__/namespaces.cpython-312.pyc,,
odf/__pycache__/number.cpython-312.pyc,,
odf/__pycache__/odf2moinmoin.cpython-312.pyc,,
odf/__pycache__/odf2xhtml.cpython-312.pyc,,
odf/__pycache__/odfmanifest.cpython-312.pyc,,
odf/__pycache__/office.cpython-312.pyc,,
odf/__pycache__/opendocument.cpython-312.pyc,,
odf/__pycache__/presentation.cpython-312.pyc,,
odf/__pycache__/script.cpython-312.pyc,,
odf/__pycache__/style.cpython-312.pyc,,
odf/__pycache__/svg.cpython-312.pyc,,
odf/__pycache__/table.cpython-312.pyc,,
odf/__pycache__/teletype.cpython-312.pyc,,
odf/__pycache__/text.cpython-312.pyc,,
odf/__pycache__/thumbnail.cpython-312.pyc,,
odf/__pycache__/userfield.cpython-312.pyc,,
odf/__pycache__/xforms.cpython-312.pyc,,
odf/anim.py,sha256=7Udql7UPQ-EwZGXEvYA4BTz-1PwgYiZKB2BOaofc1kc,1878
odf/attrconverters.py,sha256=nhfDB7RGOLj-uj7-kfdJ3vwGtbhM_fJsdebb1PrrIFE,75783
odf/chart.py,sha256=8WURg1Qd_0yNHKKKsg67MRh7gCzUsZt0sLhTyTQ8jG4,2936
odf/config.py,sha256=QFHpBUYdaSUX4FdBW8r7gK6rCr8Mha1QHbo1D5Lm0mo,1426
odf/dc.py,sha256=BCYDsmHcR77HwVplol72Z1qAY6p5h5fUlyGlT5wHDFY,2231
odf/dr3d.py,sha256=e2isME-r23LsJI81_gqs5fHg0h5oNl-fKJeppK8-Oa8,1520
odf/draw.py,sha256=g-c-sZu0kGIEIEH_EV5sQdQGsg61hJbViPNAsHV8yaY,5817
odf/easyliststyle.py,sha256=3hCB2bhliMV7OYnSfEoMqbwttRgee7B-t4FE0XR5tO4,3880
odf/element.py,sha256=azHExPlY7duPl62KOh9WdBheTH3GG1yu8o-kpB1alhc,23005
odf/elementtypes.py,sha256=5OybuxkpFMQwc7l6rUCezApjQasa3kqQrwS8NYNjxUk,10222
odf/form.py,sha256=ZgRcnNnqI8HLS_OVgCRaFt-9fsagmHNpGZg9F3E89co,3276
odf/grammar.py,sha256=giwhnQfk2SiQHICsZhgth3QdveE5x_6nRkDxjboiIjI,220354
odf/load.py,sha256=E9-By3xL6JM7PEpAwlVONsdmTuZcJl14Yz5GcgRobKo,4021
odf/manifest.py,sha256=mSYeXv78e7zPAu_uA60gCXOReGpIsn966gm5BkQLUo0,1458
odf/math.py,sha256=9O5RPrSET50taroaoYia63dhLGt5E__rYWZIdGEk9dg,1074
odf/meta.py,sha256=jDJUyQszVlnUcJ-RNPVPJy5yXoOjXfIlV--FWFm5pjs,2182
odf/namespaces.py,sha256=wqedKqljol0UqqQVWN7QeXfQpHdoqIU3MrS8A73Qaqw,4575
odf/number.py,sha256=YqUPpzp2gacS_84O-uUUKWAO0S64hWO_fqg1JSmsHvg,3146
odf/odf2moinmoin.py,sha256=q6_8iSZkThg8PGfGA4hy3g5UKgb4uLRUquRv2mzf9rQ,18198
odf/odf2xhtml.py,sha256=d5T5elfKrzVAYsKQkvLpds_AGrHMWHgexzpkVPpgVCY,66583
odf/odfmanifest.py,sha256=UHPL84CluQhkQgroGbCa97Zn6ck55EQUCQHvw8uTE8g,3782
odf/office.py,sha256=PQGwcJkDTguEbEGcD_bjLCT9g-6GsCnMmuTQEIdFnQk,3455
odf/opendocument.py,sha256=5yfNsawC6pfddRckf_3VnC1ZdgYOQjEdsnV9QW6v9Po,38884
odf/presentation.py,sha256=IkY8OyTXrg4fbkWUdYNH3ei5U3Sm6pMsu6R8MEtvKtk,2760
odf/script.py,sha256=gq84sbX8Zl48X_8rYUaLHIoIgZULhRGHmCCPe-X958M,1112
odf/style.py,sha256=CJ5EWFunHvY_MYE_d8LNngl9r9-tkgcJm5aO_weON2k,4891
odf/svg.py,sha256=1klyRxZ2F98MYHp5Ya94aaVK_2cNuvpUwtxfy4MgtqY,1875
odf/table.py,sha256=HjQDUNe3lxYLYTmQItPUmm-VQGDGyMuoZtPhb5ZSyfg,9812
odf/teletype.py,sha256=zKsgq2iUdTAtml2073JaDPaqjavuQu7G4cFObeVw15k,5187
odf/text.py,sha256=-JHAD1gMRFi_mhTRQwOOcRMe2twb7FVWXQ9viOWHqZs,17521
odf/thumbnail.py,sha256=y1kJJFNWuGDkaA5O4H4nEU8g9C85VLa-vAv-rMU5A2I,5058
odf/userfield.py,sha256=5u82t-X78x8a2bja4z17fmkUO1MokfhKCIXJ415vw18,6338
odf/xforms.py,sha256=TchIc6KKaR-uzjr0kM2utg-jFkbvSqVduVn0lLf2LfE,1239
odfpy-1.4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
odfpy-1.4.1.dist-info/METADATA,sha256=6JRHIsUyAAxvTSMWUHegfo62E1rV4uo_9i-VuakiI0A,2773
odfpy-1.4.1.dist-info/RECORD,,
odfpy-1.4.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
odfpy-1.4.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
odfpy-1.4.1.dist-info/top_level.txt,sha256=52gL7htXz_fYOZLlmRl2N9OKdrJvQg8yT7ccr37kHco,4
