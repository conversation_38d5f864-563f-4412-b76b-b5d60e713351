# -*- coding: utf-8 -*-
# Copyright (C) 2006-2007 <PERSON><PERSON><PERSON>, European Environment Agency
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
# Contributor(s):
#

from odf.namespaces import PRESENTATIONNS
from odf.element import Element

# ODF 1.0 section 9.6 and 9.7
# Autogenerated
def AnimationGroup(**args):
    return Element(qname = (PRESENTATIONNS,'animation-group'), **args)

def Animations(**args):
    return Element(qname = (PRESENTATIONNS,'animations'), **args)

def DateTime(**args):
    return Element(qname = (PRESENTATIONNS,'date-time'), **args)

def DateTimeDecl(**args):
    return Element(qname = (PRESENTATIONNS,'date-time-decl'), **args)

def Dim(**args):
    return Element(qname = (PRESENTATIONNS,'dim'), **args)

def EventListener(**args):
    return Element(qname = (PRESENTATIONNS,'event-listener'), **args)

def Footer(**args):
    return Element(qname = (PRESENTATIONNS,'footer'), **args)

def FooterDecl(**args):
    return Element(qname = (PRESENTATIONNS,'footer-decl'), **args)

def Header(**args):
    return Element(qname = (PRESENTATIONNS,'header'), **args)

def HeaderDecl(**args):
    return Element(qname = (PRESENTATIONNS,'header-decl'), **args)

def HideShape(**args):
    return Element(qname = (PRESENTATIONNS,'hide-shape'), **args)

def HideText(**args):
    return Element(qname = (PRESENTATIONNS,'hide-text'), **args)

def Notes(**args):
    return Element(qname = (PRESENTATIONNS,'notes'), **args)

def Placeholder(**args):
    return Element(qname = (PRESENTATIONNS,'placeholder'), **args)

def Play(**args):
    return Element(qname = (PRESENTATIONNS,'play'), **args)

def Settings(**args):
    return Element(qname = (PRESENTATIONNS,'settings'), **args)

def Show(**args):
    return Element(qname = (PRESENTATIONNS,'show'), **args)

def ShowShape(**args):
    return Element(qname = (PRESENTATIONNS,'show-shape'), **args)

def ShowText(**args):
    return Element(qname = (PRESENTATIONNS,'show-text'), **args)

def Sound(**args):
    args.setdefault('type', 'simple')
    return Element(qname = (PRESENTATIONNS,'sound'), **args)

