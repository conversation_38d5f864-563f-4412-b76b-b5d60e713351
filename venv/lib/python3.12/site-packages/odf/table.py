# -*- coding: utf-8 -*-
# Copyright (C) 2006-2013 <PERSON><PERSON><PERSON>, European Environment Agency
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
# Contributor(s):
#

from odf.namespaces import TABLENS
from odf.element import Element


# Autogenerated
def Background(**args):
    return Element(qname = (TABLENS,'background'), **args)

def Body(**args):
    return Element(qname = (TABLENS,'body'), **args)

def CalculationSettings(**args):
    return Element(qname = (TABLENS,'calculation-settings'), **args)

def CellAddress(**args):
    return Element(qname = (TABLENS,'cell-address'), **args)

def CellContentChange(**args):
    return Element(qname = (TABLENS,'cell-content-change'), **args)

def CellContentDeletion(**args):
    return Element(qname = (TABLENS,'cell-content-deletion'), **args)

def CellRangeSource(**args):
    args.setdefault('type', 'simple')
    return Element(qname = (TABLENS,'cell-range-source'), **args)

def ChangeDeletion(**args):
    return Element(qname = (TABLENS,'change-deletion'), **args)

def ChangeTrackTableCell(**args):
    return Element(qname = (TABLENS,'change-track-table-cell'), **args)

def Consolidation(**args):
    return Element(qname = (TABLENS,'consolidation'), **args)

def ContentValidation(**args):
    return Element(qname = (TABLENS,'content-validation'), **args)

def ContentValidations(**args):
    return Element(qname = (TABLENS,'content-validations'), **args)

def CoveredTableCell(**args):
    return Element(qname = (TABLENS,'covered-table-cell'), **args)

def CutOffs(**args):
    return Element(qname = (TABLENS,'cut-offs'), **args)

def DataPilotDisplayInfo(**args):
    return Element(qname = (TABLENS,'data-pilot-display-info'), **args)

def DataPilotField(**args):
    return Element(qname = (TABLENS,'data-pilot-field'), **args)

def DataPilotFieldReference(**args):
    return Element(qname = (TABLENS,'data-pilot-field-reference'), **args)

def DataPilotGroup(**args):
    return Element(qname = (TABLENS,'data-pilot-group'), **args)

def DataPilotGroupMember(**args):
    return Element(qname = (TABLENS,'data-pilot-group-member'), **args)

def DataPilotGroups(**args):
    return Element(qname = (TABLENS,'data-pilot-groups'), **args)

def DataPilotLayoutInfo(**args):
    return Element(qname = (TABLENS,'data-pilot-layout-info'), **args)

def DataPilotLevel(**args):
    return Element(qname = (TABLENS,'data-pilot-level'), **args)

def DataPilotMember(**args):
    return Element(qname = (TABLENS,'data-pilot-member'), **args)

def DataPilotMembers(**args):
    return Element(qname = (TABLENS,'data-pilot-members'), **args)

def DataPilotSortInfo(**args):
    return Element(qname = (TABLENS,'data-pilot-sort-info'), **args)

def DataPilotSubtotal(**args):
    return Element(qname = (TABLENS,'data-pilot-subtotal'), **args)

def DataPilotSubtotals(**args):
    return Element(qname = (TABLENS,'data-pilot-subtotals'), **args)

def DataPilotTable(**args):
    return Element(qname = (TABLENS,'data-pilot-table'), **args)

def DataPilotTables(**args):
    return Element(qname = (TABLENS,'data-pilot-tables'), **args)

def DatabaseRange(**args):
    return Element(qname = (TABLENS,'database-range'), **args)

def DatabaseRanges(**args):
    return Element(qname = (TABLENS,'database-ranges'), **args)

def DatabaseSourceQuery(**args):
    return Element(qname = (TABLENS,'database-source-query'), **args)

def DatabaseSourceSql(**args):
    return Element(qname = (TABLENS,'database-source-sql'), **args)

def DatabaseSourceTable(**args):
    return Element(qname = (TABLENS,'database-source-table'), **args)

def DdeLink(**args):
    return Element(qname = (TABLENS,'dde-link'), **args)

def DdeLinks(**args):
    return Element(qname = (TABLENS,'dde-links'), **args)

def Deletion(**args):
    return Element(qname = (TABLENS,'deletion'), **args)

def Deletions(**args):
    return Element(qname = (TABLENS,'deletions'), **args)

def Dependencies(**args):
    return Element(qname = (TABLENS,'dependencies'), **args)

def Dependency(**args):
    return Element(qname = (TABLENS,'dependency'), **args)

def Desc(**args):
    return Element(qname = (TABLENS,'desc'), **args)

def Detective(**args):
    return Element(qname = (TABLENS,'detective'), **args)

def ErrorMacro(**args):
    return Element(qname = (TABLENS,'error-macro'), **args)

def ErrorMessage(**args):
    return Element(qname = (TABLENS,'error-message'), **args)

def EvenColumns(**args):
    return Element(qname = (TABLENS,'even-columns'), **args)

def EvenRows(**args):
    return Element(qname = (TABLENS,'even-rows'), **args)

def Filter(**args):
    return Element(qname = (TABLENS,'filter'), **args)

def FilterAnd(**args):
    return Element(qname = (TABLENS,'filter-and'), **args)

def FilterCondition(**args):
    return Element(qname = (TABLENS,'filter-condition'), **args)

def FilterOr(**args):
    return Element(qname = (TABLENS,'filter-or'), **args)

def FilterSetItem(**args):
    return Element(qname = (TABLENS,'filter-set-item'), **args)

def FirstColumn(**args):
    return Element(qname = (TABLENS,'first-column'), **args)

def FirstRow(**args):
    return Element(qname = (TABLENS,'first-row'), **args)

def HelpMessage(**args):
    return Element(qname = (TABLENS,'help-message'), **args)

def HighlightedRange(**args):
    return Element(qname = (TABLENS,'highlighted-range'), **args)

def Insertion(**args):
    return Element(qname = (TABLENS,'insertion'), **args)

def InsertionCutOff(**args):
    return Element(qname = (TABLENS,'insertion-cut-off'), **args)

def Iteration(**args):
    return Element(qname = (TABLENS,'iteration'), **args)

def LabelRange(**args):
    return Element(qname = (TABLENS,'label-range'), **args)

def LabelRanges(**args):
    return Element(qname = (TABLENS,'label-ranges'), **args)

def LastColumn(**args):
    return Element(qname = (TABLENS,'last-column'), **args)

def LastRow(**args):
    return Element(qname = (TABLENS,'last-row'), **args)

def Movement(**args):
    return Element(qname = (TABLENS,'movement'), **args)

def MovementCutOff(**args):
    return Element(qname = (TABLENS,'movement-cut-off'), **args)

def NamedExpression(**args):
    return Element(qname = (TABLENS,'named-expression'), **args)

def NamedExpressions(**args):
    return Element(qname = (TABLENS,'named-expressions'), **args)

def NamedRange(**args):
    return Element(qname = (TABLENS,'named-range'), **args)

def NullDate(**args):
    return Element(qname = (TABLENS,'null-date'), **args)

def OddColumns(**args):
    return Element(qname = (TABLENS,'odd-columns'), **args)

def OddRows(**args):
    return Element(qname = (TABLENS,'odd-rows'), **args)

def Operation(**args):
    return Element(qname = (TABLENS,'operation'), **args)

def Previous(**args):
    return Element(qname = (TABLENS,'previous'), **args)

def Scenario(**args):
    return Element(qname = (TABLENS,'scenario'), **args)

def Shapes(**args):
    return Element(qname = (TABLENS,'shapes'), **args)

def Sort(**args):
    return Element(qname = (TABLENS,'sort'), **args)

def SortBy(**args):
    return Element(qname = (TABLENS,'sort-by'), **args)

def SortGroups(**args):
    return Element(qname = (TABLENS,'sort-groups'), **args)

def SourceCellRange(**args):
    return Element(qname = (TABLENS,'source-cell-range'), **args)

def SourceRangeAddress(**args):
    return Element(qname = (TABLENS,'source-range-address'), **args)

def SourceService(**args):
    return Element(qname = (TABLENS,'source-service'), **args)

def SubtotalField(**args):
    return Element(qname = (TABLENS,'subtotal-field'), **args)

def SubtotalRule(**args):
    return Element(qname = (TABLENS,'subtotal-rule'), **args)

def SubtotalRules(**args):
    return Element(qname = (TABLENS,'subtotal-rules'), **args)

def Table(**args):
    return Element(qname = (TABLENS,'table'), **args)

def TableCell(**args):
    return Element(qname = (TABLENS,'table-cell'), **args)

def TableColumn(**args):
    return Element(qname = (TABLENS,'table-column'), **args)

def TableColumnGroup(**args):
    return Element(qname = (TABLENS,'table-column-group'), **args)

def TableColumns(**args):
    return Element(qname = (TABLENS,'table-columns'), **args)

def TableHeaderColumns(**args):
    return Element(qname = (TABLENS,'table-header-columns'), **args)

def TableHeaderRows(**args):
    return Element(qname = (TABLENS,'table-header-rows'), **args)

def TableRow(**args):
    return Element(qname = (TABLENS,'table-row'), **args)

def TableRowGroup(**args):
    return Element(qname = (TABLENS,'table-row-group'), **args)

def TableRows(**args):
    return Element(qname = (TABLENS,'table-rows'), **args)

def TableSource(**args):
    args.setdefault('type', 'simple')
    return Element(qname = (TABLENS,'table-source'), **args)

def TableTemplate(**args):
    return Element(qname = (TABLENS,'table-template'), **args)

def TargetRangeAddress(**args):
    return Element(qname = (TABLENS,'target-range-address'), **args)

def Title(**args):
    return Element(qname = (TABLENS,'title'), **args)

def TrackedChanges(**args):
    return Element(qname = (TABLENS,'tracked-changes'), **args)

