# -*- coding: utf-8 -*-
# Copyright (C) 2006-2013 <PERSON><PERSON><PERSON>, European Environment Agency
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
# Contributor(s):
#

__doc__=""" In principle the OpenDocument schema converted to python structures.
Currently it contains the legal child elements of a given element.
To be used for validation check in the API
"""
import sys, os.path
sys.path.append(os.path.dirname(__file__))
from odf.namespaces import *

# The following code is generated from the RelaxNG schema with this notice:

#       Open Document Format for Office Applications (OpenDocument) Version 1.2
#       OASIS Standard, 29 September 2011
#       Relax-NG Schema
#       Source: http://docs.oasis-open.org/office/v1.2/os/
#       Copyright (c) OASIS Open 2002-2011. All Rights Reserved.

#       All capitalized terms in the following text have the meanings assigned to them
#       in the OASIS Intellectual Property Rights Policy (the "OASIS IPR Policy"). The
#       full Policy may be found at the OASIS website.

#       This document and translations of it may be copied and furnished to others, and
#       derivative works that comment on or otherwise explain it or assist in its
#       implementation may be prepared, copied, published, and distributed, in whole or
#       in part, without restriction of any kind, provided that the above copyright
#       notice and this section are included on all such copies and derivative works.
#       However, this document itself may not be modified in any way, including by
#       removing the copyright notice or references to OASIS, except as needed for the
#       purpose of developing any document or deliverable produced by an OASIS
#       Technical Committee (in which case the rules applicable to copyrights, as set
#       forth in the OASIS IPR Policy, must be followed) or as required to translate it
#       into languages other than English.

#       The limited permissions granted above are perpetual and will not be revoked by
#       OASIS or its successors or assigns.

#       This document and the information contained herein is provided on an "AS IS"
#       basis and OASIS DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT
#       LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION HEREIN WILL NOT
#       INFRINGE ANY OWNERSHIP RIGHTS OR ANY IMPLIED WARRANTIES OF MERCHANTABILITY OR
#       FITNESS FOR A PARTICULAR PURPOSE.

allowed_children = {
	(DCNS,u'creator') : (
	),
	(DCNS,u'date') : (
	),
	(DCNS,u'description') : (
	),
	(DCNS,u'language') : (
	),
	(DCNS,u'subject') : (
	),
	(DCNS,u'title') : (
	),
# Completes Dublin Core start
#	(DCNS,'contributor') : (
#	),
#	(DCNS,'coverage') : (
#	),
#	(DCNS,'format') : (
#	),
#	(DCNS,'identifier') : (
#	),
#	(DCNS,'publisher') : (
#	),
#	(DCNS,'relation') : (
#	),
#	(DCNS,'rights') : (
#	),
#	(DCNS,'source') : (
#	),
#	(DCNS,'type') : (
#	),
# Completes Dublin Core end
	(MATHNS,u'math') : None,

	(XFORMSNS,u'model') : None,

	(ANIMNS,u'animate') : (
	),
	(ANIMNS,u'animateColor') : (
	),
	(ANIMNS,u'animateMotion') : (
	),
	(ANIMNS,u'animateTransform') : (
	),
	(ANIMNS,u'audio') : (
	),
	(ANIMNS,u'command') : (
		(ANIMNS,u'param'),
	),
# allowed_children
	(ANIMNS,u'iterate') : (
		(ANIMNS,u'animate'),
		(ANIMNS,u'animateColor'),
		(ANIMNS,u'animateMotion'),
		(ANIMNS,u'animateTransform'),
		(ANIMNS,u'audio'),
		(ANIMNS,u'command'),
		(ANIMNS,u'iterate'),
		(ANIMNS,u'par'),
		(ANIMNS,u'seq'),
		(ANIMNS,u'set'),
		(ANIMNS,u'transitionFilter'),
	),
	(ANIMNS,u'par') : (
		(ANIMNS,u'animate'),
		(ANIMNS,u'animateColor'),
		(ANIMNS,u'animateMotion'),
		(ANIMNS,u'animateTransform'),
		(ANIMNS,u'audio'),
		(ANIMNS,u'command'),
		(ANIMNS,u'iterate'),
		(ANIMNS,u'par'),
		(ANIMNS,u'seq'),
		(ANIMNS,u'set'),
		(ANIMNS,u'transitionFilter'),
	),
# allowed_children
	(ANIMNS,u'param') : (
	),
	(ANIMNS,u'seq') : (
		(ANIMNS,u'animate'),
		(ANIMNS,u'animateColor'),
		(ANIMNS,u'animateMotion'),
		(ANIMNS,u'animateTransform'),
		(ANIMNS,u'audio'),
		(ANIMNS,u'command'),
		(ANIMNS,u'iterate'),
		(ANIMNS,u'par'),
		(ANIMNS,u'seq'),
		(ANIMNS,u'set'),
		(ANIMNS,u'transitionFilter'),
	),
	(ANIMNS,u'set') : (
	),
	(ANIMNS,u'transitionFilter') : (
	),
	(CHARTNS,u'axis') : (
		(CHARTNS,u'categories'),
		(CHARTNS,u'grid'),
		(CHARTNS,u'title'),
	),
# allowed_children
	(CHARTNS,u'categories') : (
	),
	(CHARTNS,u'chart') : (
		(CHARTNS,u'footer'),
		(CHARTNS,u'legend'),
		(CHARTNS,u'plot-area'),
		(CHARTNS,u'subtitle'),
		(CHARTNS,u'title'),
		(TABLENS,u'table'),
	),
	(CHARTNS,u'data-label') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'data-point') : (
		(CHARTNS,u'data-label'),
	),
	(CHARTNS,u'domain') : (
	),
# allowed_children
	(CHARTNS,u'equation') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'error-indicator') : (
	),
	(CHARTNS,u'floor') : (
	),
	(CHARTNS,u'footer') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'grid') : (
	),
	(CHARTNS,u'label-separator') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'legend') : (
		(TEXTNS,u'p'),
	),
# allowed_children
	(CHARTNS,u'mean-value') : (
	),
	(CHARTNS,u'plot-area') : (
		(CHARTNS,u'axis'),
		(CHARTNS,u'floor'),
		(CHARTNS,u'series'),
		(CHARTNS,u'stock-gain-marker'),
		(CHARTNS,u'stock-loss-marker'),
		(CHARTNS,u'stock-range-line'),
		(CHARTNS,u'wall'),
		(DR3DNS,u'light'),
	),
	(CHARTNS,u'regression-curve') : (
		(CHARTNS,u'equation'),
	),
	(CHARTNS,u'series') : (
		(CHARTNS,u'data-label'),
		(CHARTNS,u'data-point'),
		(CHARTNS,u'domain'),
		(CHARTNS,u'error-indicator'),
		(CHARTNS,u'mean-value'),
		(CHARTNS,u'regression-curve'),
	),
	(CHARTNS,u'stock-gain-marker') : (
	),
	(CHARTNS,u'stock-loss-marker') : (
	),
# allowed_children
	(CHARTNS,u'stock-range-line') : (
	),
	(CHARTNS,u'subtitle') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'symbol-image') : (
	),
	(CHARTNS,u'title') : (
		(TEXTNS,u'p'),
	),
	(CHARTNS,u'wall') : (
	),
	(CONFIGNS,u'config-item') : (
	),
	(CONFIGNS,u'config-item-map-entry') : (
		(CONFIGNS,u'config-item'),
		(CONFIGNS,u'config-item-map-indexed'),
		(CONFIGNS,u'config-item-map-named'),
		(CONFIGNS,u'config-item-set'),
	),
	(CONFIGNS,u'config-item-map-indexed') : (
		(CONFIGNS,u'config-item-map-entry'),
	),
	(CONFIGNS,u'config-item-map-named') : (
		(CONFIGNS,u'config-item-map-entry'),
	),
# allowed_children
	(CONFIGNS,u'config-item-set') : (
		(CONFIGNS,u'config-item'),
		(CONFIGNS,u'config-item-map-indexed'),
		(CONFIGNS,u'config-item-map-named'),
		(CONFIGNS,u'config-item-set'),
	),
	(MANIFESTNS,u'algorithm') : (
	),
	(MANIFESTNS,u'encryption-data') : (
		(MANIFESTNS,u'algorithm'),
		(MANIFESTNS,u'key-derivation'),
	),
	(MANIFESTNS,u'file-entry') : (
		(MANIFESTNS,u'encryption-data'),
	),
	(MANIFESTNS,u'key-derivation') : (
	),
	(MANIFESTNS,u'manifest') : (
		(MANIFESTNS,u'file-entry'),
	),
	(NUMBERNS,u'am-pm') : (
	),
	(NUMBERNS,u'boolean') : (
	),
# allowed_children
	(NUMBERNS,u'boolean-style') : (
		(NUMBERNS,u'boolean'),
		(NUMBERNS,u'text'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
	(NUMBERNS,u'currency-style') : (
		(NUMBERNS,u'currency-symbol'),
		(NUMBERNS,u'number'),
		(NUMBERNS,u'text'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
	(NUMBERNS,u'currency-symbol') : (
	),
	(NUMBERNS,u'date-style') : (
		(NUMBERNS,u'am-pm'),
		(NUMBERNS,u'day'),
		(NUMBERNS,u'day-of-week'),
		(NUMBERNS,u'era'),
		(NUMBERNS,u'hours'),
		(NUMBERNS,u'minutes'),
		(NUMBERNS,u'month'),
		(NUMBERNS,u'quarter'),
		(NUMBERNS,u'seconds'),
		(NUMBERNS,u'text'),
		(NUMBERNS,u'week-of-year'),
		(NUMBERNS,u'year'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
# allowed_children
	(NUMBERNS,u'day') : (
	),
	(NUMBERNS,u'day-of-week') : (
	),
	(NUMBERNS,u'embedded-text') : (
	),
	(NUMBERNS,u'era') : (
	),
	(NUMBERNS,u'fraction') : (
	),
	(NUMBERNS,u'hours') : (
	),
	(NUMBERNS,u'minutes') : (
	),
	(NUMBERNS,u'month') : (
	),
	(NUMBERNS,u'number') : (
		(NUMBERNS,u'embedded-text'),
	),
	(NUMBERNS,u'number-style') : (
		(NUMBERNS,u'fraction'),
		(NUMBERNS,u'number'),
		(NUMBERNS,u'scientific-number'),
		(NUMBERNS,u'text'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
# allowed_children
	(NUMBERNS,u'percentage-style') : (
		(NUMBERNS,u'number'),
		(NUMBERNS,u'text'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
	(NUMBERNS,u'quarter') : (
	),
	(NUMBERNS,u'scientific-number') : (
	),
	(NUMBERNS,u'seconds') : (
	),
	(NUMBERNS,u'text') : (
	),
	(NUMBERNS,u'text-content') : (
	),
	(NUMBERNS,u'text-style') : (
		(NUMBERNS,u'text'),
		(NUMBERNS,u'text-content'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
# allowed_children
	(NUMBERNS,u'time-style') : (
		(NUMBERNS,u'am-pm'),
		(NUMBERNS,u'hours'),
		(NUMBERNS,u'minutes'),
		(NUMBERNS,u'seconds'),
		(NUMBERNS,u'text'),
		(STYLENS,u'map'),
		(STYLENS,u'text-properties'),
	),
# allowed_children
	(NUMBERNS,u'week-of-year') : (
	),
	(NUMBERNS,u'year') : (
	),
	(DR3DNS,u'cube') : (
	),
	(DR3DNS,u'extrude') : (
	),
	(DR3DNS,u'light') : (
	),
	(DR3DNS,u'rotate') : (
	),
	(DR3DNS,u'scene') : (
		(DR3DNS,u'cube'),
		(DR3DNS,u'extrude'),
		(DR3DNS,u'light'),
		(DR3DNS,u'rotate'),
		(DR3DNS,u'scene'),
		(DR3DNS,u'sphere'),
		(DRAWNS,u'glue-point'),
		(SVGNS,u'title'),
		(SVGNS,u'desc'),
	),
	(DR3DNS,u'sphere') : (
	),
	(DRAWNS,u'a') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
	),
# allowed_children
	(DRAWNS,u'applet') : (
		(DRAWNS,u'param'),
	),
	(DRAWNS,u'area-circle') : (
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'area-polygon') : (
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'area-rectangle') : (
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'caption') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'circle') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
# allowed_children
	(DRAWNS,u'connector') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'contour-path') : (
	),
	(DRAWNS,u'contour-polygon') : (
	),
	(DRAWNS,u'control') : (
		(DRAWNS,u'glue-point'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'custom-shape') : (
		(DRAWNS,u'enhanced-geometry'),
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
# allowed_children
	(DRAWNS,u'ellipse') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'enhanced-geometry') : (
		(DRAWNS,u'equation'),
		(DRAWNS,u'handle'),
	),
	(DRAWNS,u'equation') : (
	),
# allowed_children
	(DRAWNS,u'fill-image') : (
	),
	(DRAWNS,u'floating-frame') : (
	),
	(DRAWNS,u'frame') : (
		(DRAWNS,u'applet'),
		(DRAWNS,u'contour-path'),
		(DRAWNS,u'contour-polygon'),
		(DRAWNS,u'floating-frame'),
		(DRAWNS,u'glue-point'),
		(DRAWNS,u'image'),
		(DRAWNS,u'image-map'),
		(DRAWNS,u'object'),
		(DRAWNS,u'object-ole'),
		(DRAWNS,u'plugin'),
		(DRAWNS,u'text-box'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TABLENS,u'table'),
	),
# allowed_children
	(DRAWNS,u'g') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'glue-point'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(OFFICENS,u'event-listeners'),
	),
	(DRAWNS,u'glue-point') : (
	),
	(DRAWNS,u'gradient') : (
	),
	(DRAWNS,u'handle') : (
	),
	(DRAWNS,u'hatch') : (
	),
# allowed_children
	(DRAWNS,u'image') : (
		(OFFICENS,u'binary-data'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'image-map') : (
		(DRAWNS,u'area-circle'),
		(DRAWNS,u'area-polygon'),
		(DRAWNS,u'area-rectangle'),
	),
	(DRAWNS,u'layer') : (
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'layer-set') : (
		(DRAWNS,u'layer'),
	),
	(DRAWNS,u'line') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'marker') : (
	),
	(DRAWNS,u'measure') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
		(SVGNS,u'title'),
		(SVGNS,u'desc'),
	),
	(DRAWNS,u'object') : (
		(MATHNS,u'math'),
		(OFFICENS,u'document'),
	),
# allowed_children
	(DRAWNS,u'object-ole') : (
		(OFFICENS,u'binary-data'),
	),
	(DRAWNS,u'opacity') : (
	),
	(DRAWNS,u'page') : (
		(ANIMNS,u'animate'),
		(ANIMNS,u'animateColor'),
		(ANIMNS,u'animateMotion'),
		(ANIMNS,u'animateTransform'),
		(ANIMNS,u'audio'),
		(ANIMNS,u'command'),
		(ANIMNS,u'iterate'),
		(ANIMNS,u'par'),
		(ANIMNS,u'seq'),
		(ANIMNS,u'set'),
		(ANIMNS,u'transitionFilter'),
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'layer-set'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'forms'),
		(PRESENTATIONNS,u'animations'),
		(PRESENTATIONNS,u'notes'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
# allowed_children
	(DRAWNS,u'page-thumbnail') : (
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
	),
	(DRAWNS,u'param') : (
	),
	(DRAWNS,u'path') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'plugin') : (
		(DRAWNS,u'param'),
	),
	(DRAWNS,u'polygon') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'title'),
		(SVGNS,u'desc'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'polyline') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
# allowed_children
	(DRAWNS,u'rect') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'regular-polygon') : (
		(DRAWNS,u'glue-point'),
		(OFFICENS,u'event-listeners'),
		(SVGNS,u'desc'),
		(SVGNS,u'title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
	(DRAWNS,u'stroke-dash') : (
	),
	(DRAWNS,u'text-box') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
# allowed_children
	(FORMNS,u'button') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'checkbox') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'column') : (
		(FORMNS,u'checkbox'),
		(FORMNS,u'combobox'),
		(FORMNS,u'date'),
		(FORMNS,u'formatted-text'),
		(FORMNS,u'listbox'),
		(FORMNS,u'number'),
		(FORMNS,u'text'),
		(FORMNS,u'textarea'),
		(FORMNS,u'time'),
	),
	(FORMNS,u'combobox') : (
		(FORMNS,u'item'),
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'connection-resource') : (
	),
	(FORMNS,u'date') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'file') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'fixed-text') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
# allowed_children
	(FORMNS,u'form') : (
		(FORMNS,u'button'),
		(FORMNS,u'checkbox'),
		(FORMNS,u'combobox'),
		(FORMNS,u'connection-resource'),
		(FORMNS,u'date'),
		(FORMNS,u'file'),
		(FORMNS,u'fixed-text'),
		(FORMNS,u'form'),
		(FORMNS,u'formatted-text'),
		(FORMNS,u'frame'),
		(FORMNS,u'generic-control'),
		(FORMNS,u'grid'),
		(FORMNS,u'hidden'),
		(FORMNS,u'image'),
		(FORMNS,u'image-frame'),
		(FORMNS,u'listbox'),
		(FORMNS,u'number'),
		(FORMNS,u'password'),
		(FORMNS,u'properties'),
		(FORMNS,u'radio'),
		(FORMNS,u'text'),
		(FORMNS,u'textarea'),
		(FORMNS,u'time'),
		(FORMNS,u'value-range'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'formatted-text') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'frame') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'generic-control') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'grid') : (
		(FORMNS,u'column'),
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'hidden') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'image') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
# allowed_children
	(FORMNS,u'image-frame') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'item') : (
	),
	(FORMNS,u'list-property') : (
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
		(FORMNS,u'list-value'),
	),
	(FORMNS,u'list-value') : (
	),
	(FORMNS,u'listbox') : (
		(FORMNS,u'option'),
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
# allowed_children
	(FORMNS,u'number') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'option') : (
	),
	(FORMNS,u'password') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'properties') : (
		(FORMNS,u'list-property'),
		(FORMNS,u'property'),
	),
	(FORMNS,u'property') : (
	),
	(FORMNS,u'radio') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'text') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'textarea') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
		(TEXTNS,u'p'),
	),
# allowed_children
	(FORMNS,u'time') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(FORMNS,u'value-range') : (
		(FORMNS,u'properties'),
		(OFFICENS,u'event-listeners'),
	),
	(METANS,u'auto-reload') : (
	),
	(METANS,u'creation-date') : (
	),
	(METANS,u'date-string') : (
	),
	(METANS,u'document-statistic') : (
	),
	(METANS,u'editing-cycles') : (
	),
	(METANS,u'editing-duration') : (
	),
	(METANS,u'generator') : (
	),
	(METANS,u'hyperlink-behaviour') : (
	),
	(METANS,u'initial-creator') : (
	),
	(METANS,u'keyword') : (
	),
	(METANS,u'print-date') : (
	),
	(METANS,u'printed-by') : (
	),
	(METANS,u'template') : (
	),
	(METANS,u'user-defined') : (
	),
# allowed_children
	(OFFICENS,u'annotation') : (
		(DCNS,u'creator'),
		(DCNS,u'date'),
		(METANS,u'date-string'),
		(TEXTNS,u'list'),
		(TEXTNS,u'p'),
	),
# allowed_children
	(OFFICENS,u'annotation-end') : (
	),
	(OFFICENS,u'automatic-styles') : (
		(NUMBERNS,u'boolean-style'),
		(NUMBERNS,u'currency-style'),
		(NUMBERNS,u'date-style'),
		(NUMBERNS,u'number-style'),
		(NUMBERNS,u'percentage-style'),
		(NUMBERNS,u'text-style'),
		(NUMBERNS,u'time-style'),
		(STYLENS,u'page-layout'),
		(STYLENS,u'style'),
		(TEXTNS,u'list-style'),
	),
	(OFFICENS,u'binary-data') : (
	),
	(OFFICENS,u'body') : (
		(OFFICENS,u'chart'),
		(OFFICENS,u'drawing'),
		(OFFICENS,u'image'),
		(OFFICENS,u'presentation'),
		(OFFICENS,u'spreadsheet'),
		(OFFICENS,u'text'),
	),
	(OFFICENS,u'change-info') : (
		(DCNS,u'creator'),
		(DCNS,u'date'),
		(TEXTNS,u'p'),
	),
	(OFFICENS,u'chart') : (
		(CHARTNS,u'chart'),
		(TABLENS,u'calculation-settings'),
		(TABLENS,u'consolidation'),
		(TABLENS,u'content-validations'),
		(TABLENS,u'data-pilot-tables'),
		(TABLENS,u'database-ranges'),
		(TABLENS,u'dde-links'),
		(TABLENS,u'label-ranges'),
		(TABLENS,u'named-expressions'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'variable-decls'),
	),
	(OFFICENS,u'dde-source') : (
	),
	(OFFICENS,u'document') : (
		(OFFICENS,u'automatic-styles'),
		(OFFICENS,u'body'),
		(OFFICENS,u'font-face-decls'),
		(OFFICENS,u'master-styles'),
		(OFFICENS,u'meta'),
		(OFFICENS,u'scripts'),
		(OFFICENS,u'settings'),
		(OFFICENS,u'styles'),
	),
	(OFFICENS,u'document-content') : (
		(OFFICENS,u'automatic-styles'),
		(OFFICENS,u'body'),
		(OFFICENS,u'font-face-decls'),
		(OFFICENS,u'scripts'),
	),
	(OFFICENS,u'document-meta') : (
		(OFFICENS,u'meta'),
	),
	(OFFICENS,u'document-settings') : (
		(OFFICENS,u'settings'),
	),
	(OFFICENS,u'document-styles') : (
		(OFFICENS,u'automatic-styles'),
		(OFFICENS,u'font-face-decls'),
		(OFFICENS,u'master-styles'),
		(OFFICENS,u'styles'),
	),
	(OFFICENS,u'drawing') : (
		(DRAWNS,u'page'),
		(TABLENS,u'calculation-settings'),
		(TABLENS,u'consolidation'),
		(TABLENS,u'content-validations'),
		(TABLENS,u'data-pilot-tables'),
		(TABLENS,u'database-ranges'),
		(TABLENS,u'dde-links'),
		(TABLENS,u'label-ranges'),
		(TABLENS,u'named-expressions'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'variable-decls'),
	),
	(OFFICENS,u'event-listeners') : (
		(PRESENTATIONNS,u'event-listener'),
		(SCRIPTNS,u'event-listener'),
	),
	(OFFICENS,u'font-face-decls') : (
		(STYLENS,u'font-face'),
	),
# allowed_children
	(OFFICENS,u'forms') : (
		(XFORMSNS,u'model'),
		(FORMNS,u'form'),
	),
	(OFFICENS,u'image') : (
		(DRAWNS,u'frame'),
	),
	(OFFICENS,u'master-styles') : (
		(DRAWNS,u'layer-set'),
		(STYLENS,u'handout-master'),
		(STYLENS,u'master-page'),
	),
	(OFFICENS,u'meta') : (
		(DCNS,u'creator'),
		(DCNS,u'date'),
		(DCNS,u'description'),
		(DCNS,u'language'),
		(DCNS,u'subject'),
		(DCNS,u'title'),
# Completes Dublin Core start
#		(DCNS,'contributor'),
#		(DCNS,'coverage'),
#		(DCNS,'format'),
#		(DCNS,'identifier'),
#		(DCNS,'publisher'),
#		(DCNS,'relation'),
#		(DCNS,'rights'),
#		(DCNS,'source'),
#		(DCNS,'type'),
# Completes Dublin Core end
		(METANS,u'auto-reload'),
		(METANS,u'creation-date'),
		(METANS,u'document-statistic'),
		(METANS,u'editing-cycles'),
		(METANS,u'editing-duration'),
		(METANS,u'generator'),
		(METANS,u'hyperlink-behaviour'),
		(METANS,u'initial-creator'),
		(METANS,u'keyword'),
		(METANS,u'print-date'),
		(METANS,u'printed-by'),
		(METANS,u'template'),
		(METANS,u'user-defined'),
	),
	(OFFICENS,u'presentation') : (
		(DRAWNS,u'page'),
		(PRESENTATIONNS,u'date-time-decl'),
		(PRESENTATIONNS,u'footer-decl'),
		(PRESENTATIONNS,u'header-decl'),
		(PRESENTATIONNS,u'settings'),
		(TABLENS,u'calculation-settings'),
		(TABLENS,u'consolidation'),
		(TABLENS,u'content-validations'),
		(TABLENS,u'data-pilot-tables'),
		(TABLENS,u'database-ranges'),
		(TABLENS,u'dde-links'),
		(TABLENS,u'label-ranges'),
		(TABLENS,u'named-expressions'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'variable-decls'),
	),
# allowed_children
	(OFFICENS,u'script') : None,

	(OFFICENS,u'scripts') : (
		(OFFICENS,u'event-listeners'),
		(OFFICENS,u'script'),
	),
	(OFFICENS,u'settings') : (
		(CONFIGNS,u'config-item-set'),
	),
	(OFFICENS,u'spreadsheet') : (
		(TABLENS,u'calculation-settings'),
		(TABLENS,u'consolidation'),
		(TABLENS,u'content-validations'),
		(TABLENS,u'data-pilot-tables'),
		(TABLENS,u'database-ranges'),
		(TABLENS,u'dde-links'),
		(TABLENS,u'label-ranges'),
		(TABLENS,u'named-expressions'),
		(TABLENS,u'table'),
		(TABLENS,u'tracked-changes'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'variable-decls'),
	),
	(OFFICENS,u'styles') : (
		(NUMBERNS,u'boolean-style'),
		(NUMBERNS,u'currency-style'),
		(NUMBERNS,u'date-style'),
		(NUMBERNS,u'number-style'),
		(NUMBERNS,u'percentage-style'),
		(NUMBERNS,u'text-style'),
		(NUMBERNS,u'time-style'),
		(DRAWNS,u'fill-image'),
		(DRAWNS,u'gradient'),
		(DRAWNS,u'hatch'),
		(DRAWNS,u'marker'),
		(DRAWNS,u'opacity'),
		(DRAWNS,u'stroke-dash'),
		(STYLENS,u'default-page-layout'),
		(STYLENS,u'default-style'),
		(STYLENS,u'presentation-page-layout'),
		(STYLENS,u'style'),
		(SVGNS,u'linearGradient'),
		(SVGNS,u'radialGradient'),
		(TABLENS,u'table-template'),
		(TEXTNS,u'bibliography-configuration'),
		(TEXTNS,u'linenumbering-configuration'),
		(TEXTNS,u'list-style'),
		(TEXTNS,u'notes-configuration'),
		(TEXTNS,u'outline-style'),
	),
	(OFFICENS,u'text') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'forms'),
		(TABLENS,u'calculation-settings'),
		(TABLENS,u'consolidation'),
		(TABLENS,u'content-validations'),
		(TABLENS,u'data-pilot-tables'),
		(TABLENS,u'database-ranges'),
		(TABLENS,u'dde-links'),
		(TABLENS,u'label-ranges'),
		(TABLENS,u'named-expressions'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'page-sequence'),
		(TEXTNS,u'section'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'tracked-changes'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'user-index'),
		(TEXTNS,u'variable-decls'),
	),
	(PRESENTATIONNS,u'animation-group') : (
		(PRESENTATIONNS,u'dim'),
		(PRESENTATIONNS,u'hide-shape'),
		(PRESENTATIONNS,u'hide-text'),
		(PRESENTATIONNS,u'play'),
		(PRESENTATIONNS,u'show-shape'),
		(PRESENTATIONNS,u'show-text'),
	),
	(PRESENTATIONNS,u'animations') : (
		(PRESENTATIONNS,u'animation-group'),
		(PRESENTATIONNS,u'dim'),
		(PRESENTATIONNS,u'hide-shape'),
		(PRESENTATIONNS,u'hide-text'),
		(PRESENTATIONNS,u'play'),
		(PRESENTATIONNS,u'show-shape'),
		(PRESENTATIONNS,u'show-text'),
	),
	(PRESENTATIONNS,u'date-time') : (
	),
	(PRESENTATIONNS,u'date-time-decl') : (
	),
	(PRESENTATIONNS,u'dim') : (
		(PRESENTATIONNS,u'sound'),
	),
	(PRESENTATIONNS,u'event-listener') : (
		(PRESENTATIONNS,u'sound'),
	),
	(PRESENTATIONNS,u'footer') : (
	),
	(PRESENTATIONNS,u'footer-decl') : (
	),
	(PRESENTATIONNS,u'header') : (
	),
	(PRESENTATIONNS,u'header-decl') : (
	),
	(PRESENTATIONNS,u'hide-shape') : (
		(PRESENTATIONNS,u'sound'),
	),
	(PRESENTATIONNS,u'hide-text') : (
		(PRESENTATIONNS,u'sound'),
	),
# allowed_children
	(PRESENTATIONNS,u'notes') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'forms'),
	),
	(PRESENTATIONNS,u'placeholder') : (
	),
	(PRESENTATIONNS,u'play') : (
	),
	(PRESENTATIONNS,u'settings') : (
		(PRESENTATIONNS,u'show'),
	),
	(PRESENTATIONNS,u'show') : (
	),
	(PRESENTATIONNS,u'show-shape') : (
		(PRESENTATIONNS,u'sound'),
	),
	(PRESENTATIONNS,u'show-text') : (
		(PRESENTATIONNS,u'sound'),
	),
	(PRESENTATIONNS,u'sound') : (
	),
	(SCRIPTNS,u'event-listener') : (
	),
	(STYLENS,u'background-image') : (
		(OFFICENS,u'binary-data'),
	),
# allowed_children
	(STYLENS,u'chart-properties') : (
		(CHARTNS,u'label-separator'),
		(CHARTNS,u'symbol-image'),
	),
	(STYLENS,u'column') : (
	),
	(STYLENS,u'column-sep') : (
	),
	(STYLENS,u'columns') : (
		(STYLENS,u'column'),
		(STYLENS,u'column-sep'),
	),
# allowed_children
	(STYLENS,u'default-page-layout') : (
		(STYLENS,u'page-layout-properties'),
		(STYLENS,u'header-style'),
		(STYLENS,u'footer-style'),
	),
	(STYLENS,u'default-style') : (
		(STYLENS,u'chart-properties'),
		(STYLENS,u'drawing-page-properties'),
		(STYLENS,u'graphic-properties'),
		(STYLENS,u'paragraph-properties'),
		(STYLENS,u'ruby-properties'),
		(STYLENS,u'section-properties'),
		(STYLENS,u'table-cell-properties'),
		(STYLENS,u'table-column-properties'),
		(STYLENS,u'table-properties'),
		(STYLENS,u'table-row-properties'),
		(STYLENS,u'text-properties'),
	),
	(STYLENS,u'drawing-page-properties') : (
		(PRESENTATIONNS,u'sound'),
	),
	(STYLENS,u'drop-cap') : (
	),
	(STYLENS,u'font-face') : (
		(SVGNS,u'definition-src'),
		(SVGNS,u'font-face-src'),
	),
	(STYLENS,u'footer') : (
		(STYLENS,u'region-center'),
		(STYLENS,u'region-left'),
		(STYLENS,u'region-right'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'tracked-changes'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'user-index'),
		(TEXTNS,u'variable-decls'),
	),
# allowed_children
	(STYLENS,u'footer-left') : (
		(STYLENS,u'region-center'),
		(STYLENS,u'region-left'),
		(STYLENS,u'region-right'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'tracked-changes'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'user-index'),
		(TEXTNS,u'variable-decls'),
	),
	(STYLENS,u'footer-style') : (
		(STYLENS,u'header-footer-properties'),
	),
	(STYLENS,u'footnote-sep') : (
	),
	(STYLENS,u'graphic-properties') : (
		(STYLENS,u'background-image'),
		(STYLENS,u'columns'),
		(TEXTNS,u'list-style'),
	),
# allowed_children
	(STYLENS,u'handout-master') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
	),
	(STYLENS,u'header') : (
		(STYLENS,u'region-center'),
		(STYLENS,u'region-left'),
		(STYLENS,u'region-right'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'tracked-changes'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'user-index'),
		(TEXTNS,u'variable-decls'),
	),
# allowed_children
	(STYLENS,u'header-footer-properties') : (
		(STYLENS,u'background-image'),
	),
	(STYLENS,u'header-left') : (
		(STYLENS,u'region-center'),
		(STYLENS,u'region-left'),
		(STYLENS,u'region-right'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'alphabetical-index-auto-mark-file'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'dde-connection-decls'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'sequence-decls'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'tracked-changes'),
		(TEXTNS,u'user-field-decls'),
		(TEXTNS,u'user-index'),
		(TEXTNS,u'variable-decls'),
	),
	(STYLENS,u'header-style') : (
		(STYLENS,u'header-footer-properties'),
	),
# allowed_children
	(STYLENS,u'list-level-label-alignment') : (
	),
	(STYLENS,u'list-level-properties') : (
		(STYLENS,u'list-level-label-alignment'),
	),
	(STYLENS,u'map') : (
	),
	(STYLENS,u'master-page') : (
		(ANIMNS,u'animate'),
		(ANIMNS,u'animateColor'),
		(ANIMNS,u'animateMotion'),
		(ANIMNS,u'animateTransform'),
		(ANIMNS,u'audio'),
		(ANIMNS,u'command'),
		(ANIMNS,u'iterate'),
		(ANIMNS,u'par'),
		(ANIMNS,u'seq'),
		(ANIMNS,u'set'),
		(ANIMNS,u'transitionFilter'),
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'layer-set'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'forms'),
		(PRESENTATIONNS,u'notes'),
		(STYLENS,u'footer'),
		(STYLENS,u'footer-left'),
		(STYLENS,u'header'),
		(STYLENS,u'header-left'),
	),
	(STYLENS,u'page-layout') : (
		(STYLENS,u'footer-style'),
		(STYLENS,u'header-style'),
		(STYLENS,u'page-layout-properties'),
	),
	(STYLENS,u'page-layout-properties') : (
		(STYLENS,u'background-image'),
		(STYLENS,u'columns'),
		(STYLENS,u'footnote-sep'),
	),
# allowed_children
	(STYLENS,u'paragraph-properties') : (
		(STYLENS,u'background-image'),
		(STYLENS,u'drop-cap'),
		(STYLENS,u'tab-stops'),
	),
	(STYLENS,u'presentation-page-layout') : (
		(PRESENTATIONNS,u'placeholder'),
	),
	(STYLENS,u'region-center') : (
		(TEXTNS,u'p'),
	),
	(STYLENS,u'region-left') : (
		(TEXTNS,u'p'),
	),
	(STYLENS,u'region-right') : (
		(TEXTNS,u'p'),
	),
	(STYLENS,u'ruby-properties') : (
	),
	(STYLENS,u'section-properties') : (
		(STYLENS,u'background-image'),
		(STYLENS,u'columns'),
		(TEXTNS,u'notes-configuration'),
	),
	(STYLENS,u'style') : (
		(STYLENS,u'chart-properties'),
		(STYLENS,u'drawing-page-properties'),
		(STYLENS,u'graphic-properties'),
		(STYLENS,u'map'),
		(STYLENS,u'paragraph-properties'),
		(STYLENS,u'ruby-properties'),
		(STYLENS,u'section-properties'),
		(STYLENS,u'table-cell-properties'),
		(STYLENS,u'table-column-properties'),
		(STYLENS,u'table-properties'),
		(STYLENS,u'table-row-properties'),
		(STYLENS,u'text-properties'),
	),
	(STYLENS,u'tab-stop') : (
	),
	(STYLENS,u'tab-stops') : (
		(STYLENS,u'tab-stop'),
	),
# allowed_children
	(STYLENS,u'table-cell-properties') : (
		(STYLENS,u'background-image'),
	),
	(STYLENS,u'table-column-properties') : (
	),
	(STYLENS,u'table-properties') : (
		(STYLENS,u'background-image'),
	),
	(STYLENS,u'table-row-properties') : (
		(STYLENS,u'background-image'),
	),
	(STYLENS,u'text-properties') : (
	),
	(SVGNS,u'definition-src') : (
	),
	(SVGNS,u'desc') : (
	),
	(SVGNS,u'font-face-format') : (
	),
	(SVGNS,u'font-face-name') : (
	),
	(SVGNS,u'font-face-src') : (
		(SVGNS,u'font-face-name'),
		(SVGNS,u'font-face-uri'),
	),
	(SVGNS,u'font-face-uri') : (
		(SVGNS,u'font-face-format'),
	),
	(SVGNS,u'linearGradient') : (
		(SVGNS,u'stop'),
	),
	(SVGNS,u'radialGradient') : (
		(SVGNS,u'stop'),
	),
	(SVGNS,u'stop') : (
	),
	(SVGNS,u'title') : (
	),
# allowed_children
	(TABLENS,u'background') : (
	),
	(TABLENS,u'body') : (
	),
	(TABLENS,u'calculation-settings') : (
		(TABLENS,u'iteration'),
		(TABLENS,u'null-date'),
	),
# allowed_children
	(TABLENS,u'cell-address') : (
	),
	(TABLENS,u'cell-content-change') : (
		(OFFICENS,u'change-info'),
		(TABLENS,u'cell-address'),
		(TABLENS,u'deletions'),
		(TABLENS,u'dependencies'),
		(TABLENS,u'previous'),
	),
	(TABLENS,u'cell-content-deletion') : (
		(TABLENS,u'cell-address'),
		(TABLENS,u'change-track-table-cell'),
	),
	(TABLENS,u'cell-range-source') : (
	),
	(TABLENS,u'change-deletion') : (
	),
	(TABLENS,u'change-track-table-cell') : (
		(TEXTNS,u'p'),
	),
	(TABLENS,u'consolidation') : (
	),
	(TABLENS,u'content-validation') : (
		(OFFICENS,u'event-listeners'),
		(TABLENS,u'error-macro'),
		(TABLENS,u'error-message'),
		(TABLENS,u'help-message'),
	),
# allowed_children
	(TABLENS,u'content-validations') : (
		(TABLENS,u'content-validation'),
	),
	(TABLENS,u'covered-table-cell') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(TABLENS,u'cell-range-source'),
		(TABLENS,u'detective'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
# allowed_children
	(TABLENS,u'cut-offs') : (
		(TABLENS,u'insertion-cut-off'),
		(TABLENS,u'movement-cut-off'),
	),
	(TABLENS,u'data-pilot-display-info') : (
	),
	(TABLENS,u'data-pilot-field') : (
		(TABLENS,u'data-pilot-field-reference'),
		(TABLENS,u'data-pilot-groups'),
		(TABLENS,u'data-pilot-level'),
	),
	(TABLENS,u'data-pilot-field-reference') : (
	),
	(TABLENS,u'data-pilot-group') : (
		(TABLENS,u'data-pilot-group-member'),
	),
	(TABLENS,u'data-pilot-group-member') : (
	),
	(TABLENS,u'data-pilot-groups') : (
		(TABLENS,u'data-pilot-group'),
	),
	(TABLENS,u'data-pilot-layout-info') : (
	),
	(TABLENS,u'data-pilot-level') : (
		(TABLENS,u'data-pilot-display-info'),
		(TABLENS,u'data-pilot-layout-info'),
		(TABLENS,u'data-pilot-members'),
		(TABLENS,u'data-pilot-sort-info'),
		(TABLENS,u'data-pilot-subtotals'),
	),
	(TABLENS,u'data-pilot-member') : (
	),
	(TABLENS,u'data-pilot-members') : (
		(TABLENS,u'data-pilot-member'),
	),
	(TABLENS,u'data-pilot-sort-info') : (
	),
	(TABLENS,u'data-pilot-subtotal') : (
	),
	(TABLENS,u'data-pilot-subtotals') : (
		(TABLENS,u'data-pilot-subtotal'),
	),
# allowed_children
	(TABLENS,u'data-pilot-table') : (
		(TABLENS,u'data-pilot-field'),
		(TABLENS,u'database-source-query'),
		(TABLENS,u'database-source-sql'),
		(TABLENS,u'database-source-table'),
		(TABLENS,u'source-cell-range'),
		(TABLENS,u'source-service'),
	),
	(TABLENS,u'data-pilot-tables') : (
		(TABLENS,u'data-pilot-table'),
	),
	(TABLENS,u'database-range') : (
		(TABLENS,u'database-source-query'),
		(TABLENS,u'database-source-sql'),
		(TABLENS,u'database-source-table'),
		(TABLENS,u'filter'),
		(TABLENS,u'sort'),
		(TABLENS,u'subtotal-rules'),
	),
	(TABLENS,u'database-ranges') : (
		(TABLENS,u'database-range'),
	),
	(TABLENS,u'database-source-query') : (
	),
	(TABLENS,u'database-source-sql') : (
	),
	(TABLENS,u'database-source-table') : (
	),
# allowed_children
	(TABLENS,u'dde-link') : (
		(OFFICENS,u'dde-source'),
		(TABLENS,u'table'),
	),
	(TABLENS,u'dde-links') : (
		(TABLENS,u'dde-link'),
	),
	(TABLENS,u'deletion') : (
		(OFFICENS,u'change-info'),
		(TABLENS,u'cut-offs'),
		(TABLENS,u'deletions'),
		(TABLENS,u'dependencies'),
	),
	(TABLENS,u'deletions') : (
		(TABLENS,u'cell-content-deletion'),
		(TABLENS,u'change-deletion'),
	),
	(TABLENS,u'dependencies') : (
		(TABLENS,u'dependency'),
	),
	(TABLENS,u'dependency') : (
	),
# allowed_children
	(TABLENS,u'desc') : (
	),
	(TABLENS,u'detective') : (
		(TABLENS,u'highlighted-range'),
		(TABLENS,u'operation'),
	),
# allowed_children
	(TABLENS,u'error-macro') : (
	),
	(TABLENS,u'error-message') : (
		(TEXTNS,u'p'),
	),
	(TABLENS,u'even-columns') : (
	),
	(TABLENS,u'even-rows') : (
	),
	(TABLENS,u'filter') : (
		(TABLENS,u'filter-and'),
		(TABLENS,u'filter-condition'),
		(TABLENS,u'filter-or'),
	),
	(TABLENS,u'filter-and') : (
		(TABLENS,u'filter-condition'),
		(TABLENS,u'filter-or'),
	),
	(TABLENS,u'filter-condition') : (
		(TABLENS,u'filter-set-item'),
	),
	(TABLENS,u'filter-or') : (
		(TABLENS,u'filter-and'),
		(TABLENS,u'filter-condition'),
	),
# allowed_children
	(TABLENS,u'filter-set-item') : (
	),
# allowed_children
	(TABLENS,u'first-column') : (
	),
	(TABLENS,u'first-row') : (
	),
	(TABLENS,u'help-message') : (
		(TEXTNS,u'p'),
	),
	(TABLENS,u'highlighted-range') : (
	),
	(TABLENS,u'insertion') : (
		(OFFICENS,u'change-info'),
		(TABLENS,u'deletions'),
		(TABLENS,u'dependencies'),
	),
	(TABLENS,u'insertion-cut-off') : (
	),
	(TABLENS,u'iteration') : (
	),
	(TABLENS,u'label-range') : (
	),
	(TABLENS,u'label-ranges') : (
		(TABLENS,u'label-range'),
	),
	(TABLENS,u'last-column') : (
	),
	(TABLENS,u'last-row') : (
	),
	(TABLENS,u'movement') : (
		(OFFICENS,u'change-info'),
		(TABLENS,u'deletions'),
		(TABLENS,u'dependencies'),
		(TABLENS,u'source-range-address'),
		(TABLENS,u'target-range-address'),
	),
	(TABLENS,u'movement-cut-off') : (
	),
	(TABLENS,u'named-expression') : (
	),
	(TABLENS,u'named-expressions') : (
		(TABLENS,u'named-expression'),
		(TABLENS,u'named-range'),
	),
# allowed_children
	(TABLENS,u'named-range') : (
	),
	(TABLENS,u'null-date') : (
	),
	(TABLENS,u'odd-columns') : (
	),
	(TABLENS,u'odd-rows') : (
	),
	(TABLENS,u'operation') : (
	),
	(TABLENS,u'previous') : (
		(TABLENS,u'change-track-table-cell'),
	),
	(TABLENS,u'scenario') : (
	),
	(TABLENS,u'shapes') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
	),
# allowed_children
	(TABLENS,u'sort') : (
		(TABLENS,u'sort-by'),
	),
	(TABLENS,u'sort-by') : (
	),
	(TABLENS,u'sort-groups') : (
	),
	(TABLENS,u'source-cell-range') : (
		(TABLENS,u'filter'),
	),
	(TABLENS,u'source-range-address') : (
	),
	(TABLENS,u'source-service') : (
	),
	(TABLENS,u'subtotal-field') : (
	),
	(TABLENS,u'subtotal-rule') : (
		(TABLENS,u'subtotal-field'),
	),
	(TABLENS,u'subtotal-rules') : (
		(TABLENS,u'sort-groups'),
		(TABLENS,u'subtotal-rule'),
	),
# allowed_children
	(TABLENS,u'table') : (
		(OFFICENS,u'dde-source'),
		(OFFICENS,u'forms'),
		(TABLENS,u'desc'),
		(TABLENS,u'named-expressions'),
		(TABLENS,u'scenario'),
		(TABLENS,u'shapes'),
		(TABLENS,u'table-column'),
		(TABLENS,u'table-column-group'),
		(TABLENS,u'table-columns'),
		(TABLENS,u'table-header-columns'),
		(TABLENS,u'table-header-rows'),
		(TABLENS,u'table-row'),
		(TABLENS,u'table-row-group'),
		(TABLENS,u'table-rows'),
		(TABLENS,u'table-source'),
		(TABLENS,u'title'),
		(TEXTNS,u'soft-page-break'),
	),
	(TABLENS,u'table-cell') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(TABLENS,u'cell-range-source'),
		(TABLENS,u'detective'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
# allowed_children
	(TABLENS,u'table-column') : (
	),
	(TABLENS,u'table-column-group') : (
		(TABLENS,u'table-column'),
		(TABLENS,u'table-column-group'),
		(TABLENS,u'table-columns'),
		(TABLENS,u'table-header-columns'),
	),
	(TABLENS,u'table-columns') : (
		(TABLENS,u'table-column'),
	),
	(TABLENS,u'table-header-columns') : (
		(TABLENS,u'table-column'),
	),
	(TABLENS,u'table-header-rows') : (
		(TABLENS,u'table-row'),
		(TEXTNS,u'soft-page-break'),
	),
	(TABLENS,u'table-row') : (
		(TABLENS,u'covered-table-cell'),
		(TABLENS,u'table-cell'),
	),
	(TABLENS,u'table-row-group') : (
		(TABLENS,u'table-header-rows'),
		(TABLENS,u'table-row'),
		(TABLENS,u'table-row-group'),
		(TABLENS,u'table-rows'),
		(TEXTNS,u'soft-page-break'),
	),
	(TABLENS,u'table-rows') : (
		(TABLENS,u'table-row'),
		(TEXTNS,u'soft-page-break'),
	),
# allowed_children
	(TABLENS,u'table-source') : (
	),
	(TABLENS,u'table-template') : (
		(TABLENS,u'background'),
		(TABLENS,u'body'),
		(TABLENS,u'even-columns'),
		(TABLENS,u'even-rows'),
		(TABLENS,u'first-column'),
		(TABLENS,u'first-row'),
		(TABLENS,u'last-column'),
		(TABLENS,u'last-row'),
		(TABLENS,u'odd-columns'),
		(TABLENS,u'odd-rows'),
	),
	(TABLENS,u'target-range-address') : (
	),
# allowed_children
	(TABLENS,u'title') : (
	),
	(TABLENS,u'tracked-changes') : (
		(TABLENS,u'cell-content-change'),
		(TABLENS,u'deletion'),
		(TABLENS,u'insertion'),
		(TABLENS,u'movement'),
	),
# allowed_children
	(TEXTNS,u'a') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(OFFICENS,u'event-listeners'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),

	),
# allowed_children
	(TEXTNS,u'alphabetical-index') : (
		(TEXTNS,u'alphabetical-index-source'),
		(TEXTNS,u'index-body'),
	),
	(TEXTNS,u'alphabetical-index-auto-mark-file') : (
	),
	(TEXTNS,u'alphabetical-index-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
	(TEXTNS,u'alphabetical-index-mark') : (
	),
	(TEXTNS,u'alphabetical-index-mark-end') : (
	),
	(TEXTNS,u'alphabetical-index-mark-start') : (
	),
	(TEXTNS,u'alphabetical-index-source') : (
		(TEXTNS,u'alphabetical-index-entry-template'),
		(TEXTNS,u'index-title-template'),
	),
	(TEXTNS,u'author-initials') : (
	),
	(TEXTNS,u'author-name') : (
	),
	(TEXTNS,u'bibliography') : (
		(TEXTNS,u'bibliography-source'),
		(TEXTNS,u'index-body'),
	),
	(TEXTNS,u'bibliography-configuration') : (
		(TEXTNS,u'sort-key'),
	),
	(TEXTNS,u'bibliography-entry-template') : (
		(TEXTNS,u'index-entry-bibliography'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
	),
# allowed_children
	(TEXTNS,u'bibliography-mark') : (
	),
	(TEXTNS,u'bibliography-source') : (
		(TEXTNS,u'bibliography-entry-template'),
		(TEXTNS,u'index-title-template'),
	),
	(TEXTNS,u'bookmark') : (
	),
	(TEXTNS,u'bookmark-end') : (
	),
	(TEXTNS,u'bookmark-ref') : (
	),
	(TEXTNS,u'bookmark-start') : (
	),
	(TEXTNS,u'change') : (
	),
	(TEXTNS,u'change-end') : (
	),
	(TEXTNS,u'change-start') : (
	),
	(TEXTNS,u'changed-region') : (
		(TEXTNS,u'deletion'),
		(TEXTNS,u'format-change'),
		(TEXTNS,u'insertion'),
	),
	(TEXTNS,u'chapter') : (
	),
	(TEXTNS,u'character-count') : (
	),
	(TEXTNS,u'conditional-text') : (
	),
	(TEXTNS,u'creation-date') : (
	),
	(TEXTNS,u'creation-time') : (
	),
	(TEXTNS,u'creator') : (
	),
	(TEXTNS,u'database-display') : (
		(FORMNS,u'connection-resource'),
	),
	(TEXTNS,u'database-name') : (
		(FORMNS,u'connection-resource'),
	),
	(TEXTNS,u'database-next') : (
		(FORMNS,u'connection-resource'),
	),
	(TEXTNS,u'database-row-number') : (
		(FORMNS,u'connection-resource'),
	),
	(TEXTNS,u'database-row-select') : (
		(FORMNS,u'connection-resource'),
	),
	(TEXTNS,u'date') : (
	),
	(TEXTNS,u'dde-connection') : (
	),
	(TEXTNS,u'dde-connection-decl') : (
	),
	(TEXTNS,u'dde-connection-decls') : (
		(TEXTNS,u'dde-connection-decl'),
	),
# allowed_children
	(TEXTNS,u'deletion') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'change-info'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
	(TEXTNS,u'description') : (
	),
	(TEXTNS,u'editing-cycles') : (
	),
	(TEXTNS,u'editing-duration') : (
	),
	(TEXTNS,u'execute-macro') : (
		(OFFICENS,u'event-listeners'),
	),
	(TEXTNS,u'expression') : (
	),
	(TEXTNS,u'file-name') : (
	),
	(TEXTNS,u'format-change') : (
		(OFFICENS,u'change-info'),
	),
# allowed_children
	(TEXTNS,u'h') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'number'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),

	),
# allowed_children
	(TEXTNS,u'hidden-paragraph') : (
	),
	(TEXTNS,u'hidden-text') : (
	),
	(TEXTNS,u'illustration-index') : (
		(TEXTNS,u'illustration-index-source'),
		(TEXTNS,u'index-body'),
	),
	(TEXTNS,u'illustration-index-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
	(TEXTNS,u'illustration-index-source') : (
		(TEXTNS,u'illustration-index-entry-template'),
		(TEXTNS,u'index-title-template'),
	),
	(TEXTNS,u'image-count') : (
	),
# allowed_children
	(TEXTNS,u'index-body') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
	(TEXTNS,u'index-entry-bibliography') : (
	),
	(TEXTNS,u'index-entry-chapter') : (
	),
	(TEXTNS,u'index-entry-link-end') : (
	),
	(TEXTNS,u'index-entry-link-start') : (
	),
	(TEXTNS,u'index-entry-page-number') : (
	),
	(TEXTNS,u'index-entry-span') : (
	),
	(TEXTNS,u'index-entry-tab-stop') : (
	),
	(TEXTNS,u'index-entry-text') : (
	),
	(TEXTNS,u'index-source-style') : (
	),
	(TEXTNS,u'index-source-styles') : (
		(TEXTNS,u'index-source-style'),
	),
# allowed_children
	(TEXTNS,u'index-title') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'index-title'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
	(TEXTNS,u'index-title-template') : (
	),
	(TEXTNS,u'initial-creator') : (
	),
	(TEXTNS,u'insertion') : (
		(OFFICENS,u'change-info'),
	),
	(TEXTNS,u'keywords') : (
	),
	(TEXTNS,u'line-break') : (
	),
	(TEXTNS,u'linenumbering-configuration') : (
		(TEXTNS,u'linenumbering-separator'),
	),
	(TEXTNS,u'linenumbering-separator') : (
	),
	(TEXTNS,u'list') : (
		(TEXTNS,u'list-header'),
		(TEXTNS,u'list-item'),
	),
	(TEXTNS,u'list-header') : (
		(TEXTNS,u'h'),
		(TEXTNS,u'list'),
		(TEXTNS,u'number'),
		(TEXTNS,u'p'),
		(TEXTNS,u'soft-page-break'),
	),
	(TEXTNS,u'list-item') : (
		(TEXTNS,u'h'),
		(TEXTNS,u'list'),
		(TEXTNS,u'number'),
		(TEXTNS,u'p'),
		(TEXTNS,u'soft-page-break'),
	),
	(TEXTNS,u'list-level-style-bullet') : (
		(STYLENS,u'list-level-properties'),
		(STYLENS,u'text-properties'),
	),
	(TEXTNS,u'list-level-style-image') : (
		(OFFICENS,u'binary-data'),
		(STYLENS,u'list-level-properties'),
	),
	(TEXTNS,u'list-level-style-number') : (
		(STYLENS,u'list-level-properties'),
		(STYLENS,u'text-properties'),
	),
	(TEXTNS,u'list-style') : (
		(TEXTNS,u'list-level-style-bullet'),
		(TEXTNS,u'list-level-style-image'),
		(TEXTNS,u'list-level-style-number'),
	),
	(TEXTNS,u'measure') : (
	),
# allowed_children
	(TEXTNS,u'meta') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),
	),
# allowed_children
	(TEXTNS,u'meta-field') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),
	),
# allowed_children
	(TEXTNS,u'modification-date') : (
	),
	(TEXTNS,u'modification-time') : (
	),
	(TEXTNS,u'note') : (
		(TEXTNS,u'note-body'),
		(TEXTNS,u'note-citation'),
	),
# allowed_children
	(TEXTNS,u'note-body') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
	(TEXTNS,u'note-citation') : (
	),
	(TEXTNS,u'note-continuation-notice-backward') : (
	),
	(TEXTNS,u'note-continuation-notice-forward') : (
	),
	(TEXTNS,u'note-ref') : (
	),
	(TEXTNS,u'notes-configuration') : (
		(TEXTNS,u'note-continuation-notice-backward'),
		(TEXTNS,u'note-continuation-notice-forward'),
	),
	(TEXTNS,u'number') : (
	),
	(TEXTNS,u'numbered-paragraph') : (
		(TEXTNS,u'h'),
		(TEXTNS,u'number'),
		(TEXTNS,u'p'),
	),
	(TEXTNS,u'object-count') : (
	),
	(TEXTNS,u'object-index') : (
		(TEXTNS,u'index-body'),
		(TEXTNS,u'object-index-source'),
	),
	(TEXTNS,u'object-index-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
	(TEXTNS,u'object-index-source') : (
		(TEXTNS,u'index-title-template'),
		(TEXTNS,u'object-index-entry-template'),
	),
	(TEXTNS,u'outline-level-style') : (
		(STYLENS,u'list-level-properties'),
		(STYLENS,u'text-properties'),
	),
	(TEXTNS,u'outline-style') : (
		(TEXTNS,u'outline-level-style'),
	),
# allowed_children
	(TEXTNS,u'p') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),
	),
	(TEXTNS,u'page') : (
	),
	(TEXTNS,u'page-count') : (
	),
	(TEXTNS,u'page-continuation') : (
	),
	(TEXTNS,u'page-number') : (
	),
	(TEXTNS,u'page-sequence') : (
		(TEXTNS,u'page'),
	),
	(TEXTNS,u'page-variable-get') : (
	),
	(TEXTNS,u'page-variable-set') : (
	),
	(TEXTNS,u'paragraph-count') : (
	),
	(TEXTNS,u'placeholder') : (
	),
	(TEXTNS,u'print-date') : (
	),
	(TEXTNS,u'print-time') : (
	),
	(TEXTNS,u'printed-by') : (
	),
	(TEXTNS,u'reference-mark') : (
	),
	(TEXTNS,u'reference-mark-end') : (
	),
# allowed_children
	(TEXTNS,u'reference-mark-start') : (
	),
	(TEXTNS,u'reference-ref') : (
	),
	(TEXTNS,u'ruby') : (
		(TEXTNS,u'ruby-base'),
		(TEXTNS,u'ruby-text'),
	),
	(TEXTNS,u'ruby-base') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
		(TEXTNS,u'word-count'),

	),
# allowed_children
	(TEXTNS,u'ruby-text') : (
	),
	(TEXTNS,u's') : (
	),
	(TEXTNS,u'script') : (
	),
	(TEXTNS,u'section') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'dde-source'),
		(TABLENS,u'table'),
		(TEXTNS,u'alphabetical-index'),
		(TEXTNS,u'bibliography'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'h'),
		(TEXTNS,u'illustration-index'),
		(TEXTNS,u'list'),
		(TEXTNS,u'numbered-paragraph'),
		(TEXTNS,u'object-index'),
		(TEXTNS,u'p'),
		(TEXTNS,u'section'),
		(TEXTNS,u'section-source'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'table-index'),
		(TEXTNS,u'table-of-content'),
		(TEXTNS,u'user-index'),
	),
	(TEXTNS,u'section-source') : (
	),
	(TEXTNS,u'sender-city') : (
	),
	(TEXTNS,u'sender-company') : (
	),
	(TEXTNS,u'sender-country') : (
	),
# allowed_children
	(TEXTNS,u'sender-email') : (
	),
	(TEXTNS,u'sender-fax') : (
	),
	(TEXTNS,u'sender-firstname') : (
	),
	(TEXTNS,u'sender-initials') : (
	),
	(TEXTNS,u'sender-lastname') : (
	),
	(TEXTNS,u'sender-phone-private') : (
	),
	(TEXTNS,u'sender-phone-work') : (
	),
	(TEXTNS,u'sender-position') : (
	),
	(TEXTNS,u'sender-postal-code') : (
	),
	(TEXTNS,u'sender-state-or-province') : (
	),
	(TEXTNS,u'sender-street') : (
	),
	(TEXTNS,u'sender-title') : (
	),
	(TEXTNS,u'sequence') : (
	),
	(TEXTNS,u'sequence-decl') : (
	),
	(TEXTNS,u'sequence-decls') : (
		(TEXTNS,u'sequence-decl'),
	),
	(TEXTNS,u'sequence-ref') : (
	),
	(TEXTNS,u'sheet-name') : (
	),
	(TEXTNS,u'soft-page-break') : (
	),
	(TEXTNS,u'sort-key') : (
	),
# allowed_children
	(TEXTNS,u'span') : (
		(DR3DNS,u'scene'),
		(DRAWNS,u'a'),
		(DRAWNS,u'caption'),
		(DRAWNS,u'circle'),
		(DRAWNS,u'connector'),
		(DRAWNS,u'control'),
		(DRAWNS,u'custom-shape'),
		(DRAWNS,u'ellipse'),
		(DRAWNS,u'frame'),
		(DRAWNS,u'g'),
		(DRAWNS,u'line'),
		(DRAWNS,u'measure'),
		(DRAWNS,u'page-thumbnail'),
		(DRAWNS,u'path'),
		(DRAWNS,u'polygon'),
		(DRAWNS,u'polyline'),
		(DRAWNS,u'rect'),
		(DRAWNS,u'regular-polygon'),
		(OFFICENS,u'annotation'),
		(OFFICENS,u'annotation-end'),
		(PRESENTATIONNS,u'date-time'),
		(PRESENTATIONNS,u'footer'),
		(PRESENTATIONNS,u'header'),
		(TEXTNS,u'a'),
		(TEXTNS,u'alphabetical-index-mark'),
		(TEXTNS,u'alphabetical-index-mark-end'),
		(TEXTNS,u'alphabetical-index-mark-start'),
		(TEXTNS,u'author-initials'),
		(TEXTNS,u'author-name'),
		(TEXTNS,u'bibliography-mark'),
		(TEXTNS,u'bookmark'),
		(TEXTNS,u'bookmark-end'),
		(TEXTNS,u'bookmark-ref'),
		(TEXTNS,u'bookmark-start'),
		(TEXTNS,u'change'),
		(TEXTNS,u'change-end'),
		(TEXTNS,u'change-start'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'conditional-text'),
		(TEXTNS,u'creation-date'),
		(TEXTNS,u'creation-time'),
		(TEXTNS,u'creator'),
		(TEXTNS,u'database-display'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'database-next'),
		(TEXTNS,u'database-row-number'),
		(TEXTNS,u'database-row-select'),
		(TEXTNS,u'date'),
		(TEXTNS,u'dde-connection'),
		(TEXTNS,u'description'),
		(TEXTNS,u'editing-cycles'),
		(TEXTNS,u'editing-duration'),
		(TEXTNS,u'execute-macro'),
		(TEXTNS,u'expression'),
		(TEXTNS,u'file-name'),
		(TEXTNS,u'hidden-paragraph'),
		(TEXTNS,u'hidden-text'),
		(TEXTNS,u'initial-creator'),
		(TEXTNS,u'keywords'),
		(TEXTNS,u'line-break'),
		(TEXTNS,u'measure'),
		(TEXTNS,u'meta'),
		(TEXTNS,u'meta-field'),
		(TEXTNS,u'modification-date'),
		(TEXTNS,u'modification-time'),
		(TEXTNS,u'note'),
		(TEXTNS,u'note-ref'),
		(TEXTNS,u'page-count'),
		(TEXTNS,u'paragraph-count'),
		(TEXTNS,u'word-count'),
		(TEXTNS,u'character-count'),
		(TEXTNS,u'table-count'),
		(TEXTNS,u'image-count'),
		(TEXTNS,u'object-count'),
		(TEXTNS,u'page-continuation'),
		(TEXTNS,u'page-number'),
		(TEXTNS,u'page-variable-get'),
		(TEXTNS,u'page-variable-set'),
		(TEXTNS,u'placeholder'),
		(TEXTNS,u'print-date'),
		(TEXTNS,u'print-time'),
		(TEXTNS,u'printed-by'),
		(TEXTNS,u'reference-mark'),
		(TEXTNS,u'reference-mark-end'),
		(TEXTNS,u'reference-mark-start'),
		(TEXTNS,u'reference-ref'),
		(TEXTNS,u'ruby'),
		(TEXTNS,u's'),
		(TEXTNS,u'script'),
		(TEXTNS,u'sender-city'),
		(TEXTNS,u'sender-company'),
		(TEXTNS,u'sender-country'),
		(TEXTNS,u'sender-email'),
		(TEXTNS,u'sender-fax'),
		(TEXTNS,u'sender-firstname'),
		(TEXTNS,u'sender-initials'),
		(TEXTNS,u'sender-lastname'),
		(TEXTNS,u'sender-phone-private'),
		(TEXTNS,u'sender-phone-work'),
		(TEXTNS,u'sender-position'),
		(TEXTNS,u'sender-postal-code'),
		(TEXTNS,u'sender-state-or-province'),
		(TEXTNS,u'sender-street'),
		(TEXTNS,u'sender-title'),
		(TEXTNS,u'sequence'),
		(TEXTNS,u'sequence-ref'),
		(TEXTNS,u'sheet-name'),
		(TEXTNS,u'soft-page-break'),
		(TEXTNS,u'span'),
		(TEXTNS,u'subject'),
		(TEXTNS,u'tab'),
		(TEXTNS,u'table-formula'),
		(TEXTNS,u'template-name'),
		(TEXTNS,u'text-input'),
		(TEXTNS,u'time'),
		(TEXTNS,u'title'),
		(TEXTNS,u'toc-mark'),
		(TEXTNS,u'toc-mark-end'),
		(TEXTNS,u'toc-mark-start'),
		(TEXTNS,u'user-defined'),
		(TEXTNS,u'user-field-get'),
		(TEXTNS,u'user-field-input'),
		(TEXTNS,u'user-index-mark'),
		(TEXTNS,u'user-index-mark-end'),
		(TEXTNS,u'user-index-mark-start'),
		(TEXTNS,u'variable-get'),
		(TEXTNS,u'variable-input'),
		(TEXTNS,u'variable-set'),
	),
# allowed_children
	(TEXTNS,u'subject') : (
	),
	(TEXTNS,u'tab') : (
	),
	(TEXTNS,u'table-count') : (
	),
	(TEXTNS,u'table-formula') : (
	),
	(TEXTNS,u'table-index') : (
		(TEXTNS,u'index-body'),
		(TEXTNS,u'table-index-source'),
	),
	(TEXTNS,u'table-index-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
	(TEXTNS,u'table-index-source') : (
		(TEXTNS,u'index-title-template'),
		(TEXTNS,u'table-index-entry-template'),
	),
	(TEXTNS,u'table-of-content') : (
		(TEXTNS,u'index-body'),
		(TEXTNS,u'table-of-content-source'),
	),
	(TEXTNS,u'table-of-content-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-link-end'),
		(TEXTNS,u'index-entry-link-start'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
	(TEXTNS,u'table-of-content-source') : (
		(TEXTNS,u'index-source-styles'),
		(TEXTNS,u'index-title-template'),
		(TEXTNS,u'table-of-content-entry-template'),
	),
	(TEXTNS,u'template-name') : (
	),
	(TEXTNS,u'text-input') : (
	),
	(TEXTNS,u'time') : (
	),
	(TEXTNS,u'title') : (
	),
	(TEXTNS,u'toc-mark') : (
	),
	(TEXTNS,u'toc-mark-end') : (
	),
	(TEXTNS,u'toc-mark-start') : (
	),
# allowed_children
	(TEXTNS,u'tracked-changes') : (
		(TEXTNS,u'changed-region'),
	),
	(TEXTNS,u'user-defined') : (
	),
	(TEXTNS,u'user-field-decl') : (
	),
	(TEXTNS,u'user-field-decls') : (
		(TEXTNS,u'user-field-decl'),
	),
	(TEXTNS,u'user-field-get') : (
	),
	(TEXTNS,u'user-field-input') : (
	),
	(TEXTNS,u'user-index') : (
		(TEXTNS,u'index-body'),
		(TEXTNS,u'user-index-source'),
	),
	(TEXTNS,u'user-index-entry-template') : (
		(TEXTNS,u'index-entry-chapter'),
		(TEXTNS,u'index-entry-page-number'),
		(TEXTNS,u'index-entry-span'),
		(TEXTNS,u'index-entry-tab-stop'),
		(TEXTNS,u'index-entry-text'),
	),
# allowed_children
	(TEXTNS,u'user-index-mark') : (
	),
	(TEXTNS,u'user-index-mark-end') : (
	),
	(TEXTNS,u'user-index-mark-start') : (
	),
	(TEXTNS,u'user-index-source') : (
		(TEXTNS,u'index-source-styles'),
		(TEXTNS,u'index-title-template'),
		(TEXTNS,u'user-index-entry-template'),
	),
	(TEXTNS,u'variable-decl') : (
	),
	(TEXTNS,u'variable-decls') : (
		(TEXTNS,u'variable-decl'),
	),
	(TEXTNS,u'variable-get') : (
	),
	(TEXTNS,u'variable-input') : (
	),
	(TEXTNS,u'variable-set') : (
	),
	(TEXTNS,u'word-count') : (
	),
}

#
# List of elements that allows text nodes
#
allows_text = (
	(CONFIGNS,u'config-item'),
	(DCNS,u'creator'),
	(DCNS,u'date'),
	(DCNS,u'description'),
	(DCNS,u'language'),
	(DCNS,u'subject'),
	(DCNS,u'title'),
# Completes Dublin Core start
#	(DCNS,'contributor'),
#	(DCNS,'coverage'),
#	(DCNS,'format'),
#	(DCNS,'identifier'),
#	(DCNS,'publisher'),
#	(DCNS,'relation'),
#	(DCNS,'rights'),
#	(DCNS,'source'),
#	(DCNS,'type'),
# Completes Dublin Core end
	(FORMNS,u'item'),
	(FORMNS,u'option'),
	(MATHNS,u'math'),
	(METANS,u'creation-date'),
	(METANS,u'date-string'),
	(METANS,u'editing-cycles'),
	(METANS,u'editing-duration'),
# allows_text
	(METANS,u'generator'),
	(METANS,u'initial-creator'),
	(METANS,u'keyword'),
	(METANS,u'print-date'),
	(METANS,u'printed-by'),
	(METANS,u'user-defined'),
	(NUMBERNS,u'currency-symbol'),
	(NUMBERNS,u'embedded-text'),
	(NUMBERNS,u'text'),
	(OFFICENS,u'binary-data'),
	(OFFICENS,u'script'),
	(PRESENTATIONNS,u'date-time-decl'),
	(PRESENTATIONNS,u'footer-decl'),
	(PRESENTATIONNS,u'header-decl'),
	(SVGNS,u'desc'),
	(SVGNS,u'title'),
	(TABLENS,u'desc'),
	(TABLENS,u'title'),
	(TEXTNS,u'a'),
	(TEXTNS,u'author-initials'),
	(TEXTNS,u'author-name'),
	(TEXTNS,u'bibliography-mark'),
	(TEXTNS,u'bookmark-ref'),
	(TEXTNS,u'chapter'),
	(TEXTNS,u'character-count'),
	(TEXTNS,u'conditional-text'),
	(TEXTNS,u'creation-date'),
	(TEXTNS,u'creation-time'),
	(TEXTNS,u'creator'),
	(TEXTNS,u'database-display'),
	(TEXTNS,u'database-name'),
	(TEXTNS,u'database-row-number'),
	(TEXTNS,u'date'),
	(TEXTNS,u'dde-connection'),
	(TEXTNS,u'description'),
	(TEXTNS,u'editing-cycles'),
	(TEXTNS,u'editing-duration'),
	(TEXTNS,u'execute-macro'),
	(TEXTNS,u'expression'),
	(TEXTNS,u'file-name'),
	(TEXTNS,u'h'),
	(TEXTNS,u'hidden-paragraph'),
	(TEXTNS,u'hidden-text'),
	(TEXTNS,u'image-count'),
# allows_text
	(TEXTNS,u'index-entry-span'),
	(TEXTNS,u'index-title-template'),
	(TEXTNS,u'initial-creator'),
	(TEXTNS,u'keywords'),
	(TEXTNS,u'linenumbering-separator'),
	(TEXTNS,u'measure'),
	(TEXTNS,u'meta'),
	(TEXTNS,u'meta-field'),
	(TEXTNS,u'modification-date'),
	(TEXTNS,u'modification-time'),
	(TEXTNS,u'note-citation'),
	(TEXTNS,u'note-continuation-notice-backward'),
	(TEXTNS,u'note-continuation-notice-forward'),
	(TEXTNS,u'note-ref'),
	(TEXTNS,u'number'),
	(TEXTNS,u'object-count'),
	(TEXTNS,u'p'),
	(TEXTNS,u'page-continuation'),
	(TEXTNS,u'page-count'),
	(TEXTNS,u'page-number'),
	(TEXTNS,u'page-variable-get'),
	(TEXTNS,u'page-variable-set'),
	(TEXTNS,u'paragraph-count'),
	(TEXTNS,u'placeholder'),
	(TEXTNS,u'print-date'),
	(TEXTNS,u'print-time'),
	(TEXTNS,u'printed-by'),
	(TEXTNS,u'reference-ref'),
	(TEXTNS,u'ruby-base'),
	(TEXTNS,u'ruby-text'),
# allows_text
	(TEXTNS,u'script'),
	(TEXTNS,u'sender-city'),
	(TEXTNS,u'sender-company'),
	(TEXTNS,u'sender-country'),
	(TEXTNS,u'sender-email'),
	(TEXTNS,u'sender-fax'),
	(TEXTNS,u'sender-firstname'),
	(TEXTNS,u'sender-initials'),
	(TEXTNS,u'sender-lastname'),
	(TEXTNS,u'sender-phone-private'),
	(TEXTNS,u'sender-phone-work'),
	(TEXTNS,u'sender-position'),
	(TEXTNS,u'sender-postal-code'),
	(TEXTNS,u'sender-state-or-province'),
	(TEXTNS,u'sender-street'),
	(TEXTNS,u'sender-title'),
	(TEXTNS,u'sequence'),
	(TEXTNS,u'sequence-ref'),
	(TEXTNS,u'sheet-name'),
# allows_text
	(TEXTNS,u'span'),
	(TEXTNS,u'subject'),
	(TEXTNS,u'table-count'),
	(TEXTNS,u'table-formula'),
	(TEXTNS,u'template-name'),
	(TEXTNS,u'text-input'),
	(TEXTNS,u'time'),
	(TEXTNS,u'title'),
	(TEXTNS,u'user-defined'),
	(TEXTNS,u'user-field-get'),
	(TEXTNS,u'user-field-input'),
	(TEXTNS,u'variable-get'),
	(TEXTNS,u'variable-input'),
	(TEXTNS,u'variable-set'),
	(TEXTNS,u'word-count'),
)

# Only the elements with at least one required attribute is listed

required_attributes = {
	(ANIMNS,u'animate'): (
		(SMILNS,u'attributeName'),
	),
	(ANIMNS,u'animateColor'): (
		(SMILNS,u'attributeName'),
	),
	(ANIMNS,u'animateMotion'): (
		(SMILNS,u'attributeName'),
	),
	(ANIMNS,u'animateTransform'): (
		(SVGNS,u'type'),
		(SMILNS,u'attributeName'),
	),
	(ANIMNS,u'command'): (
		(ANIMNS,u'command'),
	),
	(ANIMNS,u'param'): (
		(ANIMNS,u'name'),
		(ANIMNS,u'value'),
	),
	(ANIMNS,u'set'): (
		(SMILNS,u'attributeName'),
	),
# required_attributes
	(ANIMNS,u'transitionFilter'): (
		(SMILNS,u'type'),
	),
	(CHARTNS,u'axis'): (
		(CHARTNS,u'dimension'),
	),
	(CHARTNS,u'chart'): (
		(CHARTNS,u'class'),
	),
# required_attributes
	(CHARTNS,u'error-indicator'): (
		(CHARTNS,u'dimension'),
	),
	(CHARTNS,u'symbol-image'): (
		(XLINKNS,u'href'),
	),
	(CONFIGNS,u'config-item'): (
		(CONFIGNS,u'type'),
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-map-indexed'): (
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-map-named'): (
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-set'): (
		(CONFIGNS,u'name'),
	),
# required_attributes
	(NUMBERNS,u'boolean-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'currency-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'date-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'embedded-text'): (
		(NUMBERNS,u'position'),
	),
	(NUMBERNS,u'number-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'percentage-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'text-style'): (
		(STYLENS,u'name'),
	),
	(NUMBERNS,u'time-style'): (
		(STYLENS,u'name'),
	),
	(DR3DNS,u'extrude'): (
		(SVGNS,u'd'),
		(SVGNS,u'viewBox'),
	),
	(DR3DNS,u'light'): (
		(DR3DNS,u'direction'),
	),
	(DR3DNS,u'rotate'): (
		(SVGNS,u'viewBox'),
		(SVGNS,u'd'),
	),
# required_attributes
	(DRAWNS,u'a'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(DRAWNS,u'area-circle'): (
		(SVGNS,u'cy'),
		(SVGNS,u'cx'),
		(SVGNS,u'r'),
	),
	(DRAWNS,u'area-polygon'): (
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'points'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'area-rectangle'): (
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
	),
# required_attributes
	(DRAWNS,u'connector'): (
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'contour-path'): (
		(DRAWNS,u'recreate-on-edit'),
		(SVGNS,u'viewBox'),
		(SVGNS,u'd'),
	),
	(DRAWNS,u'contour-polygon'): (
		(DRAWNS,u'points'),
		(DRAWNS,u'recreate-on-edit'),
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'control'): (
		(DRAWNS,u'control'),
	),
	(DRAWNS,u'fill-image'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(DRAWNS,u'name'),
	),
	(DRAWNS,u'floating-frame'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(DRAWNS,u'glue-point'): (
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(DRAWNS,u'id'),
		(DRAWNS,u'escape-direction'),
	),
# required_attributes
	(DRAWNS,u'gradient'): (
		(DRAWNS,u'style'),
	),
	(DRAWNS,u'handle'): (
		(DRAWNS,u'handle-position'),
	),
	(DRAWNS,u'hatch'): (
		(DRAWNS,u'style'),
		(DRAWNS,u'name'),
	),
	(DRAWNS,u'layer'): (
		(DRAWNS,u'name'),
	),
	(DRAWNS,u'line'): (
		(SVGNS,u'y1'),
		(SVGNS,u'x2'),
		(SVGNS,u'x1'),
		(SVGNS,u'y2'),
	),
	(DRAWNS,u'marker'): (
		(SVGNS,u'd'),
		(DRAWNS,u'name'),
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'measure'): (
		(SVGNS,u'y1'),
		(SVGNS,u'x2'),
		(SVGNS,u'x1'),
		(SVGNS,u'y2'),
	),
	(DRAWNS,u'opacity'): (
		(DRAWNS,u'style'),
	),
	(DRAWNS,u'page'): (
		(DRAWNS,u'master-page-name'),
	),
	(DRAWNS,u'path'): (
		(SVGNS,u'd'),
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'plugin'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(DRAWNS,u'polygon'): (
		(DRAWNS,u'points'),
		(SVGNS,u'viewBox'),
	),
# required_attributes
	(DRAWNS,u'polyline'): (
		(DRAWNS,u'points'),
		(SVGNS,u'viewBox'),
	),
	(DRAWNS,u'regular-polygon'): (
		(DRAWNS,u'corners'),
	),
	(DRAWNS,u'stroke-dash'): (
		(DRAWNS,u'name'),
	),
	(FORMNS,u'button'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'checkbox'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'combobox'): (
		(XMLNS,u'id'),
	),
# required_attributes
	(FORMNS,u'connection-resource'): (
		(XLINKNS,u'href'),
	),
	(FORMNS,u'date'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'file'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'fixed-text'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'formatted-text'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'frame'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'generic-control'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'grid'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'hidden'): (
		(XMLNS,u'id'),
	),
# required_attributes
	(FORMNS,u'image'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'image-frame'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'list-property'): (
		(FORMNS,u'property-name'),
	),
	(FORMNS,u'list-value'): (
		(OFFICENS,u'string-value'),
	),
	(FORMNS,u'listbox'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'number'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'password'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'property'): (
		(FORMNS,u'property-name'),
	),
	(FORMNS,u'radio'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'text'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'textarea'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'time'): (
		(XMLNS,u'id'),
	),
	(FORMNS,u'value-range'): (
		(XMLNS,u'id'),
	),
	(MANIFESTNS,u'algorithm') : (
		(MANIFESTNS,u'algorithm-name'),
		(MANIFESTNS,u'initialisation-vector'),
	),
	(MANIFESTNS,u'encryption-data') : (
		(MANIFESTNS,u'checksum-type'),
		(MANIFESTNS,u'checksum'),
	),
	(MANIFESTNS,u'file-entry') : (
		(MANIFESTNS,u'full-path'),
		(MANIFESTNS,u'media-type'),
	),
	(MANIFESTNS,u'key-derivation') : (
		(MANIFESTNS,u'key-derivation-name'),
		(MANIFESTNS,u'salt'),
		(MANIFESTNS,u'iteration-count'),
	),
# required_attributes
	(METANS,u'template'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(METANS,u'user-defined'): (
		(METANS,u'name'),
	),
# required_attributes
	(OFFICENS,u'annotation-end'): (
		(OFFICENS,u'name'),
	),
	(OFFICENS,u'dde-source'): (
		(OFFICENS,u'dde-topic'),
		(OFFICENS,u'dde-application'),
		(OFFICENS,u'dde-item'),
	),
	(OFFICENS,u'document'): (
		(OFFICENS,u'mimetype'),
		(OFFICENS,u'version'),
	),
# required_attributes
	(OFFICENS,u'document-content'): (
		(OFFICENS,u'version'),
	),
# required_attributes
	(OFFICENS,u'document-meta'): (
		(OFFICENS,u'version'),
	),
# required_attributes
	(OFFICENS,u'document-settings'): (
		(OFFICENS,u'version'),
	),
# required_attributes
	(OFFICENS,u'document-styles'): (
		(OFFICENS,u'version'),
	),
	(OFFICENS,u'script'): (
		(SCRIPTNS,u'language'),
	),
	(PRESENTATIONNS,u'date-time-decl'): (
		(PRESENTATIONNS,u'source'),
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'dim'): (
		(DRAWNS,u'color'),
		(DRAWNS,u'shape-id'),
	),
# required_attributes
	(PRESENTATIONNS,u'event-listener'): (
		(PRESENTATIONNS,u'action'),
		(SCRIPTNS,u'event-name'),
	),
	(PRESENTATIONNS,u'footer-decl'): (
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'header-decl'): (
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'hide-shape'): (
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'hide-text'): (
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'placeholder'): (
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(SVGNS,u'height'),
		(PRESENTATIONNS,u'object'),
		(SVGNS,u'width'),
	),
	(PRESENTATIONNS,u'play'): (
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'show'): (
		(PRESENTATIONNS,u'name'),
		(PRESENTATIONNS,u'pages'),
	),
	(PRESENTATIONNS,u'show-shape'): (
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'show-text'): (
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'sound'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(SCRIPTNS,u'event-listener'): (
		(SCRIPTNS,u'language'),
		(SCRIPTNS,u'event-name'),
	),
	(STYLENS,u'column'): (
		(STYLENS,u'rel-width'),
	),
# required_attributes
	(STYLENS,u'column-sep'): (
		(STYLENS,u'width'),
	),
	(STYLENS,u'columns'): (
		(FONS,u'column-count'),
	),
	(STYLENS,u'font-face'): (
		(STYLENS,u'name'),
	),
	(STYLENS,u'handout-master'): (
		(STYLENS,u'page-layout-name'),
	),
	(STYLENS,u'map'): (
		(STYLENS,u'apply-style-name'),
		(STYLENS,u'condition'),
	),
# required_attributes
	(STYLENS,u'list-level-label-alignment'): (
		(TEXTNS,u'label-followed-by'),
	),
	(STYLENS,u'master-page'): (
		(STYLENS,u'page-layout-name'),
		(STYLENS,u'name'),
	),
	(STYLENS,u'page-layout'): (
		(STYLENS,u'name'),
	),
	(STYLENS,u'presentation-page-layout'): (
		(STYLENS,u'name'),
	),
	(STYLENS,u'style'): (
		(STYLENS,u'name'),
	),
	(STYLENS,u'tab-stop'): (
		(STYLENS,u'position'),
	),
	(SVGNS,u'definition-src'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(SVGNS,u'font-face-uri'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(SVGNS,u'linearGradient'): (
		(DRAWNS,u'name'),
	),
	(SVGNS,u'radialGradient'): (
		(DRAWNS,u'name'),
	),
	(SVGNS,u'stop'): (
		(SVGNS,u'offset'),
	),
# required_attributes
	(TABLENS,u'background'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'body'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'cell-address'): (
		(TABLENS,u'column'),
		(TABLENS,u'table'),
		(TABLENS,u'row'),
	),
	(TABLENS,u'cell-content-change'): (
		(TABLENS,u'id'),
	),
	(TABLENS,u'cell-range-source'): (
		(TABLENS,u'last-row-spanned'),
		(TABLENS,u'last-column-spanned'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'consolidation'): (
		(TABLENS,u'function'),
		(TABLENS,u'source-cell-range-addresses'),
		(TABLENS,u'target-cell-address'),
	),
	(TABLENS,u'content-validation'): (
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-display-info'): (
		(TABLENS,u'member-count'),
		(TABLENS,u'data-field'),
		(TABLENS,u'enabled'),
		(TABLENS,u'display-member-mode'),
	),
# required_attributes
	(TABLENS,u'data-pilot-field'): (
		(TABLENS,u'source-field-name'),
	),
	(TABLENS,u'data-pilot-field-reference'): (
		(TABLENS,u'field-name'),
		(TABLENS,u'type'),
	),
	(TABLENS,u'data-pilot-group'): (
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-group-member'): (
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-groups'): (
		(TABLENS,u'source-field-name'),
		(TABLENS,u'step'),
		(TABLENS,u'grouped-by'),
	),
	(TABLENS,u'data-pilot-layout-info'): (
		(TABLENS,u'add-empty-lines'),
		(TABLENS,u'layout-mode'),
	),
	(TABLENS,u'data-pilot-member'): (
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-sort-info'): (
		(TABLENS,u'order'),
	),
	(TABLENS,u'data-pilot-subtotal'): (
		(TABLENS,u'function'),
	),
	(TABLENS,u'data-pilot-table'): (
		(TABLENS,u'target-range-address'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'database-range'): (
		(TABLENS,u'target-range-address'),
	),
# required_attributes
	(TABLENS,u'database-source-query'): (
		(TABLENS,u'query-name'),
		(TABLENS,u'database-name'),
	),
	(TABLENS,u'database-source-sql'): (
		(TABLENS,u'database-name'),
		(TABLENS,u'sql-statement'),
	),
	(TABLENS,u'database-source-table'): (
		(TABLENS,u'database-table-name'),
		(TABLENS,u'database-name'),
	),
	(TABLENS,u'deletion'): (
		(TABLENS,u'position'),
		(TABLENS,u'type'),
		(TABLENS,u'id'),
	),
	(TABLENS,u'dependency'): (
		(TABLENS,u'id'),
	),
# required_attributes
	(TABLENS,u'even-columns'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'even-rows'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'filter-condition'): (
		(TABLENS,u'operator'),
		(TABLENS,u'field-number'),
		(TABLENS,u'value'),
	),
# required_attributes
	(TABLENS,u'filter-set-item'): (
		(TABLENS,u'value'),
	),
	(TABLENS,u'first-column'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'first-row'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'insertion'): (
		(TABLENS,u'position'),
		(TABLENS,u'type'),
		(TABLENS,u'id'),
	),
	(TABLENS,u'insertion-cut-off'): (
		(TABLENS,u'position'),
		(TABLENS,u'id'),
	),
# required_attributes
	(TABLENS,u'label-range'): (
		(TABLENS,u'label-cell-range-address'),
		(TABLENS,u'data-cell-range-address'),
		(TABLENS,u'orientation'),
	),
	(TABLENS,u'last-column'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'last-row'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'movement'): (
		(TABLENS,u'id'),
	),
	(TABLENS,u'named-expression'): (
		(TABLENS,u'expression'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'named-range'): (
		(TABLENS,u'name'),
		(TABLENS,u'cell-range-address'),
	),
	(TABLENS,u'odd-columns'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'odd-rows'): (
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'operation'): (
		(TABLENS,u'index'),
		(TABLENS,u'name'),
	),
# required_attributes
	(TABLENS,u'scenario'): (
		(TABLENS,u'is-active'),
		(TABLENS,u'scenario-ranges'),
	),
	(TABLENS,u'sort-by'): (
		(TABLENS,u'field-number'),
	),
	(TABLENS,u'source-cell-range'): (
		(TABLENS,u'cell-range-address'),
	),
	(TABLENS,u'source-service'): (
		(TABLENS,u'source-name'),
		(TABLENS,u'object-name'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'subtotal-field'): (
		(TABLENS,u'function'),
		(TABLENS,u'field-number'),
	),
	(TABLENS,u'subtotal-rule'): (
		(TABLENS,u'group-by-field-number'),
	),
	(TABLENS,u'table-source'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(TABLENS,u'table-template'): (
#		(TABLENS,u'last-row-end-column'), # Deprecated
#		(TABLENS,u'first-row-end-column'), # Deprecated
		(TABLENS,u'name'),
#		(TABLENS,u'last-row-start-column'), # Deprecated
#		(TABLENS,u'first-row-start-column'), # Deprecated
	),
	(TEXTNS,u'a'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
# required_attributes
	(TEXTNS,u'alphabetical-index'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'alphabetical-index-auto-mark-file'): (
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(TEXTNS,u'alphabetical-index-entry-template'): (
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'alphabetical-index-mark'): (
		(TEXTNS,u'string-value'),
	),
	(TEXTNS,u'alphabetical-index-mark-end'): (
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'alphabetical-index-mark-start'): (
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'bibliography'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'bibliography-entry-template'): (
		(TEXTNS,u'style-name'),
		(TEXTNS,u'bibliography-type'),
	),
	(TEXTNS,u'bibliography-mark'): (
		(TEXTNS,u'bibliography-type'),
	),
	(TEXTNS,u'bookmark'): (
		(TEXTNS,u'name'),
	),
# required_attributes
	(TEXTNS,u'bookmark-end'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'bookmark-start'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'change'): (
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'change-end'): (
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'change-start'): (
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'changed-region'): (
		(XMLNS,u'id'),
	),
	(TEXTNS,u'chapter'): (
		(TEXTNS,u'display'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'conditional-text'): (
		(TEXTNS,u'string-value-if-true'),
		(TEXTNS,u'string-value-if-false'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'database-display'): (
		(TEXTNS,u'column-name'),
		(TEXTNS,u'table-name'),
	),
	(TEXTNS,u'database-name'): (
		(TEXTNS,u'table-name'),
	),
	(TEXTNS,u'database-next'): (
		(TEXTNS,u'table-name'),
	),
	(TEXTNS,u'database-row-number'): (
		(TEXTNS,u'table-name'),
	),
	(TEXTNS,u'database-row-select'): (
		(TEXTNS,u'table-name'),
	),
	(TEXTNS,u'dde-connection'): (
		(TEXTNS,u'connection-name'),
	),
# required_attributes
	(TEXTNS,u'dde-connection-decl'): (
		(OFFICENS,u'dde-topic'),
		(OFFICENS,u'dde-application'),
		(OFFICENS,u'name'),
		(OFFICENS,u'dde-item'),
	),
	(TEXTNS,u'h'): (
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'hidden-paragraph'): (
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'hidden-text'): (
		(TEXTNS,u'string-value'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'illustration-index'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'illustration-index-entry-template'): (
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-bibliography'): (
		(TEXTNS,u'bibliography-data-field'),
	),
	(TEXTNS,u'index-source-style'): (
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-source-styles'): (
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'index-title'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'list-level-style-bullet'): (
		(TEXTNS,u'bullet-char'),
		(TEXTNS,u'level'),
	),
	(TEXTNS,u'list-level-style-image'): (
		(TEXTNS,u'level'),
	),
	(TEXTNS,u'list-level-style-number'): (
		(TEXTNS,u'level'),
	),
	(TEXTNS,u'list-style'): (
		(STYLENS,u'name'),
	),
	(TEXTNS,u'meta-field'): (
		(XMLNS,u'id'),
	),
# required_attributes
	(TEXTNS,u'measure'): (
		(TEXTNS,u'kind'),
	),
	(TEXTNS,u'note'): (
		(TEXTNS,u'note-class'),
	),
	(TEXTNS,u'note-ref'): (
		(TEXTNS,u'note-class'),
	),
	(TEXTNS,u'notes-configuration'): (
		(TEXTNS,u'note-class'),
	),
# required_attributes
	(TEXTNS,u'numbered-paragraph'): (
		(TEXTNS,u'list-id'),
	),
	(TEXTNS,u'object-index'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'object-index-entry-template'): (
		(TEXTNS,u'style-name'),
	),
# required_attributes
	(TEXTNS,u'outline-style'): (
		(STYLENS,u'name'),
	),
	(TEXTNS,u'outline-level-style'): (
		(TEXTNS,u'level'),
	),
	(TEXTNS,u'page'): (
		(TEXTNS,u'master-page-name'),
	),
	(TEXTNS,u'page-continuation'): (
		(TEXTNS,u'select-page'),
	),
	(TEXTNS,u'placeholder'): (
		(TEXTNS,u'placeholder-type'),
	),
	(TEXTNS,u'reference-mark'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'reference-mark-end'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'reference-mark-start'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'section'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'sequence'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'sequence-decl'): (
		(TEXTNS,u'display-outline-level'),
		(TEXTNS,u'name'),
	),
# required_attributes
	(TEXTNS,u'sort-key'): (
		(TEXTNS,u'key'),
	),
	(TEXTNS,u'table-index'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'table-index-entry-template'): (
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'table-of-content'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'table-of-content-entry-template'): (
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'toc-mark'): (
		(TEXTNS,u'string-value'),
	),
	(TEXTNS,u'toc-mark-end'): (
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'toc-mark-start'): (
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'user-defined'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'user-field-decl'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'user-field-get'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'user-field-input'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'user-index'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'user-index-entry-template'): (
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
# required_attributes
	(TEXTNS,u'user-index-mark'): (
		(TEXTNS,u'index-name'),
		(TEXTNS,u'string-value'),
	),
	(TEXTNS,u'user-index-mark-end'): (
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'user-index-mark-start'): (
		(TEXTNS,u'index-name'),
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'user-index-source'): (
		(TEXTNS,u'index-name'),
	),
	(TEXTNS,u'variable-decl'): (
		(TEXTNS,u'name'),
		(OFFICENS,u'value-type'),
	),
	(TEXTNS,u'variable-get'): (
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'variable-input'): (
		(TEXTNS,u'name'),
		(OFFICENS,u'value-type'),
	),
	(TEXTNS,u'variable-set'): (
		(TEXTNS,u'name'),
	),
}

# Empty list means the element has no allowed attributes
# None means anything goes

allowed_attributes = {
	(DCNS,u'creator'):(
	),
	(DCNS,u'date'):(
	),
	(DCNS,u'description'):(
	),
	(DCNS,u'language'):(
	),
	(DCNS,u'subject'):(
	),
	(DCNS,u'title'):(
	),
# Completes Dublin Core start
#	(DCNS,'contributor') : (
#	),
#	(DCNS,'coverage') : (
#	),
#	(DCNS,'format') : (
#	),
#	(DCNS,'identifier') : (
#	),
#	(DCNS,'publisher') : (
#	),
#	(DCNS,'relation') : (
#	),
#	(DCNS,'rights') : (
#	),
#	(DCNS,'source') : (
#	),
#	(DCNS,'type') : (
#	),
# Completes Dublin Core end
	(MATHNS,u'math'): None,
	(XFORMSNS,u'model'): None,
# allowed_attributes
	(ANIMNS,u'animate'):(
		(ANIMNS,u'formula'),
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'additive'),
		(SMILNS,u'attributeName'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'by'),
		(SMILNS,u'calcMode'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'from'),
		(SMILNS,u'keySplines'),
		(SMILNS,u'keyTimes'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),
		(SMILNS,u'values'),
	),
# allowed_attributes
	(ANIMNS,u'animateColor'):(
		(ANIMNS,u'color-interpolation'),
		(ANIMNS,u'color-interpolation-direction'),
		(ANIMNS,u'formula'),
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'additive'),
		(SMILNS,u'attributeName'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'by'),
		(SMILNS,u'calcMode'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'from'),
		(SMILNS,u'keySplines'),
		(SMILNS,u'keyTimes'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),
		(SMILNS,u'values'),
	),
# allowed_attributes
	(ANIMNS,u'animateMotion'):(
		(ANIMNS,u'formula'),
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'additive'),
		(SMILNS,u'attributeName'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'by'),
		(SMILNS,u'calcMode'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'from'),
		(SMILNS,u'keySplines'),
		(SMILNS,u'keyTimes'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),
		(SMILNS,u'values'),
		(SVGNS,u'origin'),
		(SVGNS,u'path'),
	),
# allowed_attributes
	(ANIMNS,u'animateTransform'):(
		(ANIMNS,u'formula'),
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'additive'),
		(SMILNS,u'attributeName'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'by'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'from'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),
		(SMILNS,u'values'),
		(SVGNS,u'type'),
	),
# allowed_attributes
	(ANIMNS,u'audio'):(
		(ANIMNS,u'audio-level'),
		(ANIMNS,u'id'),
		(PRESENTATIONNS,u'group-id'),
		(PRESENTATIONNS,u'master-element'),
		(PRESENTATIONNS,u'node-type'),
		(PRESENTATIONNS,u'preset-class'),
		(PRESENTATIONNS,u'preset-id'),
		(PRESENTATIONNS,u'preset-sub-type'),
		(SMILNS,u'begin'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(XLINKNS,u'href'),
		(XMLNS,u'id'),
	),
	(ANIMNS,u'command'):(
		(PRESENTATIONNS,u'node-type'),
		(SMILNS,u'begin'),
		(SMILNS,u'end'),
		(PRESENTATIONNS,u'group-id'),
		(PRESENTATIONNS,u'preset-class'),
		(PRESENTATIONNS,u'preset-id'),
		(ANIMNS,u'sub-item'),
		(ANIMNS,u'command'),
		(PRESENTATIONNS,u'preset-sub-type'),
		(SMILNS,u'targetElement'),
		(ANIMNS,u'id'),
		(PRESENTATIONNS,u'master-element'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(ANIMNS,u'iterate'):(
		(ANIMNS,u'id'),
		(ANIMNS,u'iterate-interval'),
		(ANIMNS,u'iterate-type'),
		(ANIMNS,u'sub-item'),
		(PRESENTATIONNS,u'group-id'),
		(PRESENTATIONNS,u'master-element'),
		(PRESENTATIONNS,u'node-type'),
		(PRESENTATIONNS,u'preset-class'),
		(PRESENTATIONNS,u'preset-id'),
		(PRESENTATIONNS,u'preset-sub-type'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'endsync'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(XMLNS,u'id'),
	),
	(ANIMNS,u'par'):(
		(PRESENTATIONNS,u'node-type'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'begin'),
		(SMILNS,u'end'),
		(PRESENTATIONNS,u'group-id'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'autoReverse'),
		(PRESENTATIONNS,u'preset-class'),
		(SMILNS,u'fillDefault'),
		(PRESENTATIONNS,u'preset-id'),
		(PRESENTATIONNS,u'preset-sub-type'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'endsync'),
		(SMILNS,u'dur'),
		(SMILNS,u'fill'),
		(ANIMNS,u'id'),
		(SMILNS,u'restart'),
		(PRESENTATIONNS,u'master-element'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(ANIMNS,u'param'):(
		(ANIMNS,u'name'),
		(ANIMNS,u'value'),
	),
	(ANIMNS,u'seq'):(
		(ANIMNS,u'id'),
		(PRESENTATIONNS,u'group-id'),
		(PRESENTATIONNS,u'master-element'),
		(PRESENTATIONNS,u'node-type'),
		(PRESENTATIONNS,u'preset-class'),
		(PRESENTATIONNS,u'preset-id'),
		(PRESENTATIONNS,u'preset-sub-type'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'endsync'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(XMLNS,u'id'),
	),
	(ANIMNS,u'set'):(
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'additive'),
		(SMILNS,u'attributeName'),
		(SMILNS,u'begin'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),

	),
# allowed_attributes
	(ANIMNS,u'transitionFilter'):(
		(ANIMNS,u'formula'),
		(ANIMNS,u'sub-item'),
		(SMILNS,u'accelerate'),
		(SMILNS,u'accumulate'),
		(SMILNS,u'additive'),
		(SMILNS,u'autoReverse'),
		(SMILNS,u'begin'),
		(SMILNS,u'by'),
		(SMILNS,u'calcMode'),
		(SMILNS,u'decelerate'),
		(SMILNS,u'direction'),
		(SMILNS,u'dur'),
		(SMILNS,u'end'),
		(SMILNS,u'fadeColor'),
		(SMILNS,u'fill'),
		(SMILNS,u'fillDefault'),
		(SMILNS,u'from'),
		(SMILNS,u'mode'),
		(SMILNS,u'repeatCount'),
		(SMILNS,u'repeatDur'),
		(SMILNS,u'restart'),
		(SMILNS,u'restartDefault'),
		(SMILNS,u'subtype'),
		(SMILNS,u'targetElement'),
		(SMILNS,u'to'),
		(SMILNS,u'type'),
		(SMILNS,u'values'),

	),
# allowed_attributes
	(CHARTNS,u'axis'):(
		(CHARTNS,u'style-name'),
		(CHARTNS,u'dimension'),
		(CHARTNS,u'name'),
	),
	(CHARTNS,u'categories'):(
		(TABLENS,u'cell-range-address'),
	),
	(CHARTNS,u'chart'):(
		(CHARTNS,u'class'),
		(CHARTNS,u'column-mapping'),
		(CHARTNS,u'row-mapping'),
		(CHARTNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(XMLNS,u'id'),
	),
	(CHARTNS,u'data-label'):(
		(CHARTNS,u'style-name'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
	),
	(CHARTNS,u'data-point'):(
		(CHARTNS,u'repeated'),
		(CHARTNS,u'style-name'),
		(XMLNS,u'id'),
	),
	(CHARTNS,u'domain'):(
		(TABLENS,u'cell-range-address'),
	),
# allowed_attributes
	(CHARTNS,u'equation'):(
		(CHARTNS,u'automatic-content'),
		(CHARTNS,u'display-equation'),
		(CHARTNS,u'display-r-square'),
		(CHARTNS,u'style-name'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
	),
	(CHARTNS,u'error-indicator'):(
		(CHARTNS,u'dimension'),
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'floor'):(
		(SVGNS,u'width'),
		(CHARTNS,u'style-name'),
	),
# allowed_attributes
	(CHARTNS,u'footer'):(
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'cell-range'),
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'grid'):(
		(CHARTNS,u'style-name'),
		(CHARTNS,u'class'),
	),
# allowed_attributes
	(CHARTNS,u'label-separator'):(
	),
	(CHARTNS,u'legend'):(
		(CHARTNS,u'legend-align'),
		(STYLENS,u'legend-expansion-aspect-ratio'),
		(STYLENS,u'legend-expansion'),
		(CHARTNS,u'legend-position'),
		(CHARTNS,u'style-name'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
	),
	(CHARTNS,u'mean-value'):(
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'plot-area'):(
		(CHARTNS,u'data-source-has-labels'),
		(CHARTNS,u'style-name'),
		(DR3DNS,u'ambient-color'),
		(DR3DNS,u'distance'),
		(DR3DNS,u'focal-length'),
		(DR3DNS,u'lighting-mode'),
		(DR3DNS,u'projection'),
		(DR3DNS,u'shade-mode'),
		(DR3DNS,u'shadow-slant'),
		(DR3DNS,u'transform'),
		(DR3DNS,u'vpn'),
		(DR3DNS,u'vrp'),
		(DR3DNS,u'vup'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'cell-range-address'),
		(XMLNS,u'id'),
	),
	(CHARTNS,u'regression-curve'):(
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'series'):(
		(CHARTNS,u'style-name'),
		(CHARTNS,u'attached-axis'),
		(CHARTNS,u'values-cell-range-address'),
		(CHARTNS,u'label-cell-address'),
		(CHARTNS,u'class'),
		(XMLNS,u'id'),
	),
	(CHARTNS,u'stock-gain-marker'):(
		(CHARTNS,u'style-name'),
	),
# allowed_attributes
	(CHARTNS,u'stock-loss-marker'):(
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'stock-range-line'):(
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'subtitle'):(
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'cell-range'),
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'symbol-image'):(
		(XLINKNS,u'href'),
	),
	(CHARTNS,u'title'):(
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'cell-range'),
		(CHARTNS,u'style-name'),
	),
	(CHARTNS,u'wall'):(
		(SVGNS,u'width'),
		(CHARTNS,u'style-name'),
	),
	(CONFIGNS,u'config-item'):(
		(CONFIGNS,u'type'),
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-map-entry'):(
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-map-indexed'):(
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-map-named'):(
		(CONFIGNS,u'name'),
	),
	(CONFIGNS,u'config-item-set'):(
		(CONFIGNS,u'name'),
	),
# allowed_attributes
	(NUMBERNS,u'am-pm'):(
	),
	(NUMBERNS,u'boolean'):(
	),
	(NUMBERNS,u'boolean-style'):(
		(NUMBERNS,u'country'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'currency-style'):(
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'automatic-order'),
		(NUMBERNS,u'country'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'currency-symbol'):(
		(NUMBERNS,u'country'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
	),
# allowed_attributes
	(NUMBERNS,u'date-style'):(
		(NUMBERNS,u'automatic-order'),
		(NUMBERNS,u'country'),
		(NUMBERNS,u'format-source'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'day'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
	),
	(NUMBERNS,u'day-of-week'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
	),
	(NUMBERNS,u'embedded-text'):(
		(NUMBERNS,u'position'),
	),
	(NUMBERNS,u'era'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
	),
	(NUMBERNS,u'fraction'):(
		(NUMBERNS,u'grouping'),
		(NUMBERNS,u'min-denominator-digits'),
		(NUMBERNS,u'min-numerator-digits'),
		(NUMBERNS,u'min-integer-digits'),
		(NUMBERNS,u'denominator-value'),
	),
	(NUMBERNS,u'hours'):(
		(NUMBERNS,u'style'),
	),
# allowed_attributes
	(NUMBERNS,u'minutes'):(
		(NUMBERNS,u'style'),
	),
	(NUMBERNS,u'month'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
		(NUMBERNS,u'possessive-form'),
		(NUMBERNS,u'textual'),
	),
	(NUMBERNS,u'number'):(
		(NUMBERNS,u'display-factor'),
		(NUMBERNS,u'decimal-places'),
		(NUMBERNS,u'decimal-replacement'),
		(NUMBERNS,u'min-integer-digits'),
		(NUMBERNS,u'grouping'),
	),
	(NUMBERNS,u'number-style'):(
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'transliteration-language'),
		(STYLENS,u'name'),
		(STYLENS,u'display-name'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'volatile'),
		(NUMBERNS,u'transliteration-country'),
	),
# allowed_attributes
	(NUMBERNS,u'percentage-style'):(
		(NUMBERNS,u'country'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'quarter'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
	),
	(NUMBERNS,u'scientific-number'):(
		(NUMBERNS,u'min-exponent-digits'),
		(NUMBERNS,u'decimal-places'),
		(NUMBERNS,u'min-integer-digits'),
		(NUMBERNS,u'grouping'),
	),
	(NUMBERNS,u'seconds'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'decimal-places'),
	),
	(NUMBERNS,u'text'):(
	),
	(NUMBERNS,u'text-content'):(
	),
	(NUMBERNS,u'text-style'):(
		(NUMBERNS,u'country'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'time-style'):(
		(NUMBERNS,u'country'),
		(NUMBERNS,u'format-source'),
		(NUMBERNS,u'language'),
		(NUMBERNS,u'rfc-language-tag'),
		(NUMBERNS,u'script'),
		(NUMBERNS,u'title'),
		(NUMBERNS,u'transliteration-country'),
		(NUMBERNS,u'transliteration-format'),
		(NUMBERNS,u'transliteration-language'),
		(NUMBERNS,u'transliteration-style'),
		(NUMBERNS,u'truncate-on-overflow'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
		(STYLENS,u'volatile'),
	),
	(NUMBERNS,u'week-of-year'):(
		(NUMBERNS,u'calendar'),
	),
	(NUMBERNS,u'year'):(
		(NUMBERNS,u'style'),
		(NUMBERNS,u'calendar'),
	),
	(DR3DNS,u'cube'):(
		(DR3DNS,u'min-edge'),
		(DR3DNS,u'max-edge'),
		(DRAWNS,u'layer'),
		(DR3DNS,u'transform'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(DRAWNS,u'id'),
		(XMLNS,u'id'),
	),
	(DR3DNS,u'extrude'):(
		(DRAWNS,u'layer'),
		(SVGNS,u'd'),
		(DR3DNS,u'transform'),
		(SVGNS,u'viewBox'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(DRAWNS,u'id'),
		(XMLNS,u'id'),
	),
	(DR3DNS,u'light'):(
		(DR3DNS,u'diffuse-color'),
		(DR3DNS,u'direction'),
		(DR3DNS,u'specular'),
		(DR3DNS,u'enabled'),
	),
	(DR3DNS,u'rotate'):(
		(DRAWNS,u'layer'),
		(SVGNS,u'd'),
		(DR3DNS,u'transform'),
		(SVGNS,u'viewBox'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(DRAWNS,u'id'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DR3DNS,u'scene'):(
		(DR3DNS,u'ambient-color'),
		(DR3DNS,u'distance'),
		(DR3DNS,u'focal-length'),
		(DR3DNS,u'lighting-mode'),
		(DR3DNS,u'projection'),
		(DR3DNS,u'shade-mode'),
		(DR3DNS,u'shadow-slant'),
		(DR3DNS,u'transform'),
		(DR3DNS,u'vpn'),
		(DR3DNS,u'vrp'),
		(DR3DNS,u'vup'),
		(DRAWNS,u'id'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DR3DNS,u'sphere'):(
		(DRAWNS,u'layer'),
		(DR3DNS,u'center'),
		(DR3DNS,u'transform'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(DRAWNS,u'id'),
		(DR3DNS,u'size'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'a'):(
		(OFFICENS,u'name'),
		(OFFICENS,u'server-map'),
		(OFFICENS,u'target-frame-name'),
		(OFFICENS,u'title'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'show'),
		(XLINKNS,u'type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'applet'):(
		(DRAWNS,u'code'),
		(XLINKNS,u'show'),
		(DRAWNS,u'object'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(DRAWNS,u'archive'),
		(DRAWNS,u'may-script'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'area-circle'):(
		(OFFICENS,u'name'),
		(XLINKNS,u'show'),
		(SVGNS,u'cx'),
		(XLINKNS,u'type'),
		(DRAWNS,u'nohref'),
		(SVGNS,u'cy'),
		(XLINKNS,u'href'),
		(SVGNS,u'r'),
		(OFFICENS,u'target-frame-name'),
	),
	(DRAWNS,u'area-polygon'):(
		(OFFICENS,u'name'),
		(XLINKNS,u'show'),
		(XLINKNS,u'type'),
		(SVGNS,u'height'),
		(DRAWNS,u'nohref'),
		(SVGNS,u'width'),
		(XLINKNS,u'href'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(OFFICENS,u'target-frame-name'),
		(SVGNS,u'viewBox'),
		(DRAWNS,u'points'),
	),
	(DRAWNS,u'area-rectangle'):(
		(OFFICENS,u'name'),
		(XLINKNS,u'show'),
		(XLINKNS,u'type'),
		(SVGNS,u'height'),
		(DRAWNS,u'nohref'),
		(SVGNS,u'width'),
		(XLINKNS,u'href'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(OFFICENS,u'target-frame-name'),
	),
	(DRAWNS,u'caption'):(
		(TABLENS,u'table-background'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'caption-id'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'caption-point-y'),
		(DRAWNS,u'caption-point-x'),
		(DRAWNS,u'transform'),
		(TABLENS,u'end-y'),
		(DRAWNS,u'corner-radius'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(SVGNS,u'height'),
		(DRAWNS,u'id'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'circle'):(
		(DRAWNS,u'end-angle'),
		(DRAWNS,u'id'),
		(DRAWNS,u'kind'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'start-angle'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'cx'),
		(SVGNS,u'cy'),
		(SVGNS,u'height'),
		(SVGNS,u'r'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'connector'):(
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'end-glue-point'),
		(DRAWNS,u'end-shape'),
		(DRAWNS,u'id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'line-skew'),
		(DRAWNS,u'name'),
		(DRAWNS,u'start-glue-point'),
		(DRAWNS,u'start-shape'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'type'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'd'),
		(SVGNS,u'viewBox'),
		(SVGNS,u'x1'),
		(SVGNS,u'x2'),
		(SVGNS,u'y1'),
		(SVGNS,u'y2'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'contour-path'):(
		(SVGNS,u'd'),
		(SVGNS,u'width'),
		(DRAWNS,u'recreate-on-edit'),
		(SVGNS,u'viewBox'),
		(SVGNS,u'height'),
	),
	(DRAWNS,u'contour-polygon'):(
		(SVGNS,u'width'),
		(DRAWNS,u'points'),
		(DRAWNS,u'recreate-on-edit'),
		(SVGNS,u'viewBox'),
		(SVGNS,u'height'),
	),
	(DRAWNS,u'control'):(
		(DRAWNS,u'control'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'caption-id'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(TABLENS,u'table-background'),
		(DRAWNS,u'transform'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(DRAWNS,u'id'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'custom-shape'):(
		(DRAWNS,u'engine'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'layer'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(TABLENS,u'table-background'),
		(DRAWNS,u'transform'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(DRAWNS,u'data'),
		(DRAWNS,u'id'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'ellipse'):(
		(DRAWNS,u'layer'),
		(DRAWNS,u'start-angle'),
		(SVGNS,u'cy'),
		(SVGNS,u'cx'),
		(TABLENS,u'table-background'),
		(TABLENS,u'end-cell-address'),
		(SVGNS,u'rx'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'id'),
		(SVGNS,u'width'),
		(TABLENS,u'end-y'),
		(TABLENS,u'end-x'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(DRAWNS,u'end-angle'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'caption-id'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(TEXTNS,u'anchor-type'),
		(SVGNS,u'ry'),
		(DRAWNS,u'kind'),
		(DRAWNS,u'name'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(DRAWNS,u'text-style-name'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'enhanced-geometry'):(
		(DR3DNS,u'projection'),
		(DR3DNS,u'shade-mode'),
		(DRAWNS,u'concentric-gradient-fill-allowed'),
		(DRAWNS,u'enhanced-path'),
		(DRAWNS,u'extrusion'),
		(DRAWNS,u'extrusion-allowed'),
		(DRAWNS,u'extrusion-brightness'),
		(DRAWNS,u'extrusion-color'),
		(DRAWNS,u'extrusion-depth'),
		(DRAWNS,u'extrusion-diffusion'),
		(DRAWNS,u'extrusion-first-light-direction'),
		(DRAWNS,u'extrusion-first-light-harsh'),
		(DRAWNS,u'extrusion-first-light-level'),
		(DRAWNS,u'extrusion-light-face'),
		(DRAWNS,u'extrusion-metal'),
		(DRAWNS,u'extrusion-number-of-line-segments'),
		(DRAWNS,u'extrusion-origin'),
		(DRAWNS,u'extrusion-rotation-angle'),
		(DRAWNS,u'extrusion-rotation-center'),
		(DRAWNS,u'extrusion-second-light-direction'),
		(DRAWNS,u'extrusion-second-light-harsh'),
		(DRAWNS,u'extrusion-second-light-level'),
		(DRAWNS,u'extrusion-shininess'),
		(DRAWNS,u'extrusion-skew'),
		(DRAWNS,u'extrusion-specularity'),
		(DRAWNS,u'extrusion-viewpoint'),
		(DRAWNS,u'glue-point-leaving-directions'),
		(DRAWNS,u'glue-points'),
		(DRAWNS,u'glue-point-type'),
		(DRAWNS,u'mirror-horizontal'),
		(DRAWNS,u'mirror-vertical'),
		(DRAWNS,u'modifiers'),
		(DRAWNS,u'path-stretchpoint-x'),
		(DRAWNS,u'path-stretchpoint-y'),
		(DRAWNS,u'text-areas'),
		(DRAWNS,u'text-path'),
		(DRAWNS,u'text-path-allowed'),
		(DRAWNS,u'text-path-mode'),
		(DRAWNS,u'text-path-same-letter-heights'),
		(DRAWNS,u'text-path-scale'),
		(DRAWNS,u'text-rotate-angle'),
		(DRAWNS,u'type'),
		(SVGNS,u'viewBox'),
	),
# allowed_attributes
	(DRAWNS,u'equation'):(
		(DRAWNS,u'formula'),
		(DRAWNS,u'name'),
	),
	(DRAWNS,u'fill-image'):(
		(DRAWNS,u'name'),
		(XLINKNS,u'show'),
		(XLINKNS,u'actuate'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(XLINKNS,u'href'),
		(DRAWNS,u'display-name'),
		(XLINKNS,u'type'),
	),
	(DRAWNS,u'floating-frame'):(
		(XLINKNS,u'href'),
		(XLINKNS,u'actuate'),
		(DRAWNS,u'frame-name'),
		(XLINKNS,u'type'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'frame'):(
		(DRAWNS,u'copy-of'),
		(DRAWNS,u'id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'placeholder'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'user-transformed'),
		(STYLENS,u'rel-height'),
		(STYLENS,u'rel-width'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'g'):(
		(DRAWNS,u'id'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'name'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'glue-point'):(
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(DRAWNS,u'align'),
		(DRAWNS,u'id'),
		(DRAWNS,u'escape-direction'),
	),
	(DRAWNS,u'gradient'):(
		(DRAWNS,u'style'),
		(DRAWNS,u'angle'),
		(DRAWNS,u'name'),
		(DRAWNS,u'end-color'),
		(DRAWNS,u'start-color'),
		(DRAWNS,u'cy'),
		(DRAWNS,u'cx'),
		(DRAWNS,u'display-name'),
		(DRAWNS,u'border'),
		(DRAWNS,u'end-intensity'),
		(DRAWNS,u'start-intensity'),
	),
	(DRAWNS,u'handle'):(
		(DRAWNS,u'handle-radius-range-minimum'),
		(DRAWNS,u'handle-switched'),
		(DRAWNS,u'handle-range-y-maximum'),
		(DRAWNS,u'handle-mirror-horizontal'),
		(DRAWNS,u'handle-range-x-maximum'),
		(DRAWNS,u'handle-mirror-vertical'),
		(DRAWNS,u'handle-range-y-minimum'),
		(DRAWNS,u'handle-radius-range-maximum'),
		(DRAWNS,u'handle-range-x-minimum'),
		(DRAWNS,u'handle-position'),
		(DRAWNS,u'handle-polar'),
	),
	(DRAWNS,u'hatch'):(
		(DRAWNS,u'distance'),
		(DRAWNS,u'style'),
		(DRAWNS,u'name'),
		(DRAWNS,u'color'),
		(DRAWNS,u'display-name'),
		(DRAWNS,u'rotation'),
	),
	(DRAWNS,u'image'):(
		(DRAWNS,u'filter-name'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'image-map'):(
	),
	(DRAWNS,u'layer'):(
		(DRAWNS,u'protected'),
		(DRAWNS,u'name'),
		(DRAWNS,u'display'),
	),
# allowed_attributes
	(DRAWNS,u'layer-set'):(
	),
	(DRAWNS,u'line'):(
		(DRAWNS,u'class-names'),
		(DRAWNS,u'id'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'x1'),
		(SVGNS,u'x2'),
		(SVGNS,u'y1'),
		(SVGNS,u'y2'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'marker'):(
		(SVGNS,u'd'),
		(DRAWNS,u'display-name'),
		(DRAWNS,u'name'),
		(SVGNS,u'viewBox'),
	),
# allowed_attributes
	(DRAWNS,u'measure'):(
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'layer'),
		(SVGNS,u'y2'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(TABLENS,u'table-background'),
		(SVGNS,u'x2'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y1'),
		(DRAWNS,u'caption-id'),
		(TABLENS,u'end-y'),
		(SVGNS,u'x1'),
		(DRAWNS,u'id'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'object'):(
		(XLINKNS,u'type'),
		(XLINKNS,u'href'),
		(DRAWNS,u'notify-on-update-of-ranges'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'object-ole'):(
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(DRAWNS,u'class-id'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'opacity'):(
		(DRAWNS,u'style'),
		(DRAWNS,u'angle'),
		(DRAWNS,u'name'),
		(DRAWNS,u'start'),
		(DRAWNS,u'cy'),
		(DRAWNS,u'cx'),
		(DRAWNS,u'end'),
		(DRAWNS,u'display-name'),
		(DRAWNS,u'border'),
	),
	(DRAWNS,u'page'):(
		(PRESENTATIONNS,u'presentation-page-layout-name'),
		(DRAWNS,u'name'),
		(DRAWNS,u'nav-order'),
		(PRESENTATIONNS,u'use-footer-name'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'use-header-name'),
		(DRAWNS,u'master-page-name'),
		(DRAWNS,u'id'),
		(PRESENTATIONNS,u'use-date-time-name'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'page-thumbnail'):(
		(TABLENS,u'table-background'),
		(DRAWNS,u'caption-id'),
		(PRESENTATIONNS,u'user-transformed'),
		(DRAWNS,u'layer'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'id'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'page-number'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(PRESENTATIONNS,u'placeholder'),
		(PRESENTATIONNS,u'class'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'param'):(
		(DRAWNS,u'name'),
		(DRAWNS,u'value'),
	),
# allowed_attributes
	(DRAWNS,u'path'):(
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'd'),
		(SVGNS,u'height'),
		(SVGNS,u'viewBox'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'plugin'):(
		(DRAWNS,u'mime-type'),
		(XLINKNS,u'type'),
		(XLINKNS,u'href'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'polygon'):(
		(DRAWNS,u'caption-id'),
		(TABLENS,u'table-background'),
		(DRAWNS,u'layer'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'id'),
		(DRAWNS,u'transform'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'points'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(SVGNS,u'viewBox'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'polyline'):(
		(TABLENS,u'table-background'),
		(DRAWNS,u'layer'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'id'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'transform'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'points'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(TEXTNS,u'anchor-page-number'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(SVGNS,u'viewBox'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'rect'):(
		(DRAWNS,u'corner-radius'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(SVGNS,u'rx'),
		(SVGNS,u'ry'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(DRAWNS,u'regular-polygon'):(
		(TABLENS,u'table-background'),
		(DRAWNS,u'layer'),
		(TABLENS,u'end-cell-address'),
		(DRAWNS,u'caption-id'),
		(DRAWNS,u'name'),
		(DRAWNS,u'text-style-name'),
		(TEXTNS,u'anchor-page-number'),
		(DRAWNS,u'concave'),
		(DRAWNS,u'sharpness'),
		(DRAWNS,u'transform'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(DRAWNS,u'z-index'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'style-name'),
		(PRESENTATIONNS,u'class-names'),
		(TABLENS,u'end-x'),
		(DRAWNS,u'corners'),
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(TABLENS,u'end-y'),
		(DRAWNS,u'id'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(DRAWNS,u'stroke-dash'):(
		(DRAWNS,u'distance'),
		(DRAWNS,u'dots1-length'),
		(DRAWNS,u'name'),
		(DRAWNS,u'dots2-length'),
		(DRAWNS,u'style'),
		(DRAWNS,u'dots1'),
		(DRAWNS,u'display-name'),
		(DRAWNS,u'dots2'),
	),
	(DRAWNS,u'text-box'):(
		(FONS,u'min-width'),
		(DRAWNS,u'corner-radius'),
		(FONS,u'max-height'),
		(FONS,u'min-height'),
		(DRAWNS,u'chain-next-name'),
		(FONS,u'max-width'),
		(TEXTNS,u'id'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(FORMNS,u'button'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'focus-on-click'),
		(FORMNS,u'image-align'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'button-type'),
		(FORMNS,u'title'),
		(FORMNS,u'default-button'),
		(FORMNS,u'value'),
		(FORMNS,u'label'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'repeat'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'image-data'),
		(XLINKNS,u'href'),
		(FORMNS,u'toggle'),
		(FORMNS,u'xforms-submission'),
		(OFFICENS,u'target-frame'),
		(FORMNS,u'image-position'),
	),
	(FORMNS,u'checkbox'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'image-align'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'data-field'),
		(FORMNS,u'title'),
		(FORMNS,u'is-tristate'),
		(FORMNS,u'current-state'),
		(FORMNS,u'value'),
		(FORMNS,u'label'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'state'),
		(FORMNS,u'visual-effect'),
		(FORMNS,u'image-position'),
	),
	(FORMNS,u'column'):(
		(FORMNS,u'control-implementation'),
		(FORMNS,u'text-style-name'),
		(FORMNS,u'name'),
		(FORMNS,u'label'),
	),
# allowed_attributes
	(FORMNS,u'combobox'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'auto-complete'),
		(FORMNS,u'control-implementation'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'current-value'),
		(FORMNS,u'data-field'),
		(FORMNS,u'disabled'),
		(FORMNS,u'dropdown'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'list-source'),
		(FORMNS,u'list-source-type'),
		(FORMNS,u'max-length'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'size'),
		(FORMNS,u'source-cell-range'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(XFORMSNS,u'bind'),
	),
# allowed_attributes
	(FORMNS,u'connection-resource'):(
		(XLINKNS,u'href'),
	),
	(FORMNS,u'date'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'control-implementation'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'current-value'),
		(FORMNS,u'data-field'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'disabled'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'max-length'),
		(FORMNS,u'max-value'),
		(FORMNS,u'min-value'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'repeat'),
		(FORMNS,u'spin-button'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(XFORMSNS,u'bind'),
	),
	(FORMNS,u'file'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'max-length'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'current-value'),
	),
	(FORMNS,u'fixed-text'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'name'),
		(FORMNS,u'for'),
		(FORMNS,u'title'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'multi-line'),
		(FORMNS,u'label'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
	),
# allowed_attributes
	(FORMNS,u'form'):(
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(FORMNS,u'allow-deletes'),
		(FORMNS,u'command-type'),
		(FORMNS,u'apply-filter'),
		(XLINKNS,u'type'),
		(FORMNS,u'method'),
		(OFFICENS,u'target-frame'),
		(FORMNS,u'navigation-mode'),
		(FORMNS,u'detail-fields'),
		(FORMNS,u'master-fields'),
		(FORMNS,u'allow-updates'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-cycle'),
		(FORMNS,u'control-implementation'),
		(FORMNS,u'escape-processing'),
		(FORMNS,u'filter'),
		(FORMNS,u'command'),
		(FORMNS,u'datasource'),
		(FORMNS,u'enctype'),
		(FORMNS,u'allow-inserts'),
		(FORMNS,u'ignore-result'),
		(FORMNS,u'order'),
	),
# allowed_attributes
	(FORMNS,u'formatted-text'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'control-implementation'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'current-value'),
		(FORMNS,u'data-field'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'disabled'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'max-length'),
		(FORMNS,u'max-value'),
		(FORMNS,u'min-value'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'repeat'),
		(FORMNS,u'spin-button'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'validation'),
		(FORMNS,u'value'),
		(XFORMSNS,u'bind'),
	),
	(FORMNS,u'frame'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'name'),
		(FORMNS,u'for'),
		(FORMNS,u'title'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'label'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
	),
# allowed_attributes
	(FORMNS,u'generic-control'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'name'),
	),
	(FORMNS,u'grid'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'title'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
	),
	(FORMNS,u'hidden'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'name'),
		(FORMNS,u'value'),
	),
	(FORMNS,u'image'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'button-type'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(OFFICENS,u'target-frame'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'image-data'),
		(XLINKNS,u'href'),
	),
	(FORMNS,u'image-frame'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'name'),
		(FORMNS,u'title'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'data-field'),
		(FORMNS,u'readonly'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'image-data'),
	),
	(FORMNS,u'item'):(
		(FORMNS,u'label'),
	),
	(FORMNS,u'list-property'):(
		(FORMNS,u'property-name'),
		(OFFICENS,u'value-type'),
	),
	(FORMNS,u'list-value'):(
		(OFFICENS,u'string-value'),
	),
# allowed_attributes
	(FORMNS,u'listbox'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'bound-column'),
		(FORMNS,u'control-implementation'),
		(FORMNS,u'data-field'),
		(FORMNS,u'disabled'),
		(FORMNS,u'dropdown'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'list-linkage-type'),
		(FORMNS,u'list-source'),
		(FORMNS,u'list-source-type'),
		(FORMNS,u'multiple'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'size'),
		(FORMNS,u'source-cell-range'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'xforms-list-source'),
		(XFORMSNS,u'bind'),
	),
	(FORMNS,u'number'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'control-implementation'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'current-value'),
		(FORMNS,u'data-field'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'disabled'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'max-length'),
		(FORMNS,u'max-value'),
		(FORMNS,u'min-value'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'repeat'),
		(FORMNS,u'spin-button'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(XFORMSNS,u'bind'),
	),
	(FORMNS,u'option'):(
		(FORMNS,u'current-selected'),
		(FORMNS,u'selected'),
		(FORMNS,u'value'),
		(FORMNS,u'label'),
	),
	(FORMNS,u'password'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'max-length'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'echo-char'),
	),
	(FORMNS,u'properties'):(
	),
	(FORMNS,u'property'):(
		(OFFICENS,u'string-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'boolean-value'),
		(FORMNS,u'property-name'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(OFFICENS,u'value-type'),
		(OFFICENS,u'time-value'),
	),
	(FORMNS,u'radio'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'selected'),
		(FORMNS,u'image-align'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'data-field'),
		(FORMNS,u'current-selected'),
		(FORMNS,u'value'),
		(FORMNS,u'label'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'title'),
		(FORMNS,u'visual-effect'),
		(FORMNS,u'image-position'),
	),
	(FORMNS,u'text'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'max-length'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'data-field'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'current-value'),
	),
	(FORMNS,u'textarea'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'max-length'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'data-field'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'current-value'),
	),
	(FORMNS,u'time'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'control-implementation'),
		(FORMNS,u'convert-empty-to-null'),
		(FORMNS,u'current-value'),
		(FORMNS,u'data-field'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'disabled'),
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'max-length'),
		(FORMNS,u'max-value'),
		(FORMNS,u'min-value'),
		(FORMNS,u'name'),
		(FORMNS,u'printable'),
		(FORMNS,u'readonly'),
		(FORMNS,u'repeat'),
		(FORMNS,u'spin-button'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(XFORMSNS,u'bind'),
	),
	(FORMNS,u'value-range'):(
		(XMLNS,u'id'), # First choice
		(FORMNS,u'id'),
		(FORMNS,u'linked-cell'),
		(FORMNS,u'tab-stop'),
		(FORMNS,u'max-value'),
		(FORMNS,u'name'),
		(FORMNS,u'tab-index'),
		(FORMNS,u'control-implementation'),
		(XFORMSNS,u'bind'),
		(FORMNS,u'title'),
		(FORMNS,u'value'),
		(FORMNS,u'disabled'),
		(FORMNS,u'printable'),
		(FORMNS,u'orientation'),
		(FORMNS,u'page-step-size'),
		(FORMNS,u'delay-for-repeat'),
		(FORMNS,u'repeat'),
		(FORMNS,u'min-value'),
		(FORMNS,u'step-size'),
	),
	(MANIFESTNS,'algorithm') : (
		(MANIFESTNS,'algorithm-name'),
		(MANIFESTNS,'initialisation-vector'),
	),
	(MANIFESTNS,'encryption-data') : (
		(MANIFESTNS,'checksum-type'),
		(MANIFESTNS,'checksum'),
	),
	(MANIFESTNS,'file-entry') : (
		(MANIFESTNS,'full-path'),
		(MANIFESTNS,'media-type'),
		(MANIFESTNS,'preferred-view-mode'),
		(MANIFESTNS,'size'),
		(MANIFESTNS,'version'),
	),
	(MANIFESTNS,'key-derivation') : (
		(MANIFESTNS,'key-derivation-name'),
		(MANIFESTNS,'salt'),
		(MANIFESTNS,'iteration-count'),
	),
	(MANIFESTNS,u'manifest'):(
	),
# allowed_attributes
	(METANS,u'auto-reload'):(
		(METANS,u'delay'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(XLINKNS,u'show'),
	),
	(METANS,u'creation-date'):(
	),
	(METANS,u'date-string'):(
	),
	(METANS,u'document-statistic'):(
		(METANS,u'non-whitespace-character-count'),
		(METANS,u'ole-object-count'),
		(METANS,u'table-count'),
		(METANS,u'row-count'),
		(METANS,u'character-count'),
		(METANS,u'sentence-count'),
		(METANS,u'draw-count'),
		(METANS,u'paragraph-count'),
		(METANS,u'word-count'),
		(METANS,u'object-count'),
		(METANS,u'syllable-count'),
		(METANS,u'image-count'),
		(METANS,u'page-count'),
		(METANS,u'frame-count'),
		(METANS,u'cell-count'),
	),
	(METANS,u'editing-cycles'):(
	),
	(METANS,u'editing-duration'):(
	),
	(METANS,u'generator'):(
	),
# allowed_attributes
	(METANS,u'hyperlink-behaviour'):(
		(OFFICENS,u'target-frame-name'),
		(XLINKNS,u'show'),
	),
	(METANS,u'initial-creator'):(
	),
	(METANS,u'keyword'):(
	),
	(METANS,u'print-date'):(
	),
	(METANS,u'printed-by'):(
	),
	(METANS,u'template'):(
		(METANS,u'date'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(XLINKNS,u'title'),
	),
	(METANS,u'user-defined'):(
		(METANS,u'name'),
		(METANS,u'value-type'),
	),
# allowed_attributes
	(OFFICENS,u'annotation'):(
		(DRAWNS,u'caption-point-x'),
		(DRAWNS,u'caption-point-y'),
		(DRAWNS,u'class-names'),
		(DRAWNS,u'corner-radius'),
		(DRAWNS,u'id'),
		(DRAWNS,u'layer'),
		(DRAWNS,u'name'),
		(DRAWNS,u'style-name'),
		(DRAWNS,u'text-style-name'),
		(DRAWNS,u'transform'),
		(DRAWNS,u'z-index'),
		(OFFICENS,u'display'),
		(OFFICENS,u'name'),
		(PRESENTATIONNS,u'class-names'),
		(PRESENTATIONNS,u'style-name'),
		(SVGNS,u'height'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TABLENS,u'end-cell-address'),
		(TABLENS,u'end-x'),
		(TABLENS,u'end-y'),
		(TABLENS,u'table-background'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(XMLNS,u'id'),
	),
	(OFFICENS,u'annotation-end'): (
		(OFFICENS,u'name'),
	),
	(OFFICENS,u'automatic-styles'):(
	),
	(OFFICENS,u'binary-data'):(
	),
	(OFFICENS,u'body'):(
	),
	(OFFICENS,u'change-info'):(
	),
	(OFFICENS,u'chart'):(
	),
# allowed_attributes
	(OFFICENS,u'dde-source'):(
		(OFFICENS,u'dde-application'),
		(OFFICENS,u'automatic-update'),
		(OFFICENS,u'conversion-mode'),
		(OFFICENS,u'dde-item'),
		(OFFICENS,u'dde-topic'),
		(OFFICENS,u'name'),
	),
	(OFFICENS,u'document'):(
		(OFFICENS,u'mimetype'),
		(OFFICENS,u'version'),
		(GRDDLNS,u'transformation'),
	),
	(OFFICENS,u'document-content'):(
		(OFFICENS,u'version'),
		(GRDDLNS,u'transformation'),
	),
	(OFFICENS,u'document-meta'):(
		(OFFICENS,u'version'),
		(GRDDLNS,u'transformation'),
	),
	(OFFICENS,u'document-settings'):(
		(OFFICENS,u'version'),
		(GRDDLNS,u'transformation'),
	),
	(OFFICENS,u'document-styles'):(
		(OFFICENS,u'version'),
		(GRDDLNS,u'transformation'),
	),
	(OFFICENS,u'drawing'):(
	),
	(OFFICENS,u'event-listeners'):(
	),
	(OFFICENS,u'font-face-decls'):(
	),
	(OFFICENS,u'forms'):(
		(FORMNS,u'automatic-focus'),
		(FORMNS,u'apply-design-mode'),
	),
	(OFFICENS,u'image'):(
	),
# allowed_attributes
	(OFFICENS,u'master-styles'):(
	),
	(OFFICENS,u'meta'):(
	),
	(OFFICENS,u'presentation'):(
	),
	(OFFICENS,u'script'):(
		(SCRIPTNS,u'language'),
	),
	(OFFICENS,u'scripts'):(
	),
	(OFFICENS,u'settings'):(
	),
	(OFFICENS,u'spreadsheet'):(
		(TABLENS,u'structure-protected'),
		(TABLENS,u'protection-key'),
		(TABLENS,u'protection-key-digest-algorithm'),
	),
	(OFFICENS,u'styles'):(
	),
	(OFFICENS,u'text'):(
		(TEXTNS,u'global'),
		(TEXTNS,u'use-soft-page-breaks'),
	),
	(PRESENTATIONNS,u'animation-group'):(
	),
	(PRESENTATIONNS,u'animations'):(
	),
	(PRESENTATIONNS,u'date-time'):(
	),
	(PRESENTATIONNS,u'date-time-decl'):(
		(PRESENTATIONNS,u'source'),
		(STYLENS,u'data-style-name'),
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'dim'):(
		(DRAWNS,u'color'),
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'event-listener'):(
		(PRESENTATIONNS,u'direction'),
		(XLINKNS,u'show'),
		(XLINKNS,u'type'),
		(XLINKNS,u'actuate'),
		(PRESENTATIONNS,u'effect'),
		(SCRIPTNS,u'event-name'),
		(PRESENTATIONNS,u'start-scale'),
		(XLINKNS,u'href'),
		(PRESENTATIONNS,u'verb'),
		(PRESENTATIONNS,u'action'),
		(PRESENTATIONNS,u'speed'),
	),
	(PRESENTATIONNS,u'footer'):(
	),
	(PRESENTATIONNS,u'footer-decl'):(
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'header'):(
	),
	(PRESENTATIONNS,u'header-decl'):(
		(PRESENTATIONNS,u'name'),
	),
	(PRESENTATIONNS,u'hide-shape'):(
		(PRESENTATIONNS,u'direction'),
		(PRESENTATIONNS,u'effect'),
		(PRESENTATIONNS,u'delay'),
		(PRESENTATIONNS,u'start-scale'),
		(PRESENTATIONNS,u'path-id'),
		(PRESENTATIONNS,u'speed'),
		(DRAWNS,u'shape-id'),
	),
# allowed_attributes
	(PRESENTATIONNS,u'hide-text'):(
		(PRESENTATIONNS,u'direction'),
		(PRESENTATIONNS,u'effect'),
		(PRESENTATIONNS,u'delay'),
		(PRESENTATIONNS,u'start-scale'),
		(PRESENTATIONNS,u'path-id'),
		(PRESENTATIONNS,u'speed'),
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'notes'):(
		(STYLENS,u'page-layout-name'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'use-header-name'),
		(PRESENTATIONNS,u'use-date-time-name'),
		(PRESENTATIONNS,u'use-footer-name'),
	),
	(PRESENTATIONNS,u'placeholder'):(
		(SVGNS,u'y'),
		(SVGNS,u'x'),
		(SVGNS,u'height'),
		(PRESENTATIONNS,u'object'),
		(SVGNS,u'width'),
	),
	(PRESENTATIONNS,u'play'):(
		(PRESENTATIONNS,u'speed'),
		(DRAWNS,u'shape-id'),
	),
# allowed_attributes
	(PRESENTATIONNS,u'settings'):(
		(PRESENTATIONNS,u'animations'),
		(PRESENTATIONNS,u'endless'),
		(PRESENTATIONNS,u'force-manual'),
		(PRESENTATIONNS,u'full-screen'),
		(PRESENTATIONNS,u'mouse-as-pen'),
		(PRESENTATIONNS,u'mouse-visible'),
		(PRESENTATIONNS,u'pause'),
		(PRESENTATIONNS,u'show'),
		(PRESENTATIONNS,u'show-end-of-presentation-slide'),
		(PRESENTATIONNS,u'show-logo'),
		(PRESENTATIONNS,u'start-page'),
		(PRESENTATIONNS,u'start-with-navigator'),
		(PRESENTATIONNS,u'stay-on-top'),
		(PRESENTATIONNS,u'transition-on-click'),
	),
	(PRESENTATIONNS,u'show'):(
		(PRESENTATIONNS,u'name'),
		(PRESENTATIONNS,u'pages'),
	),
	(PRESENTATIONNS,u'show-shape'):(
		(PRESENTATIONNS,u'direction'),
		(PRESENTATIONNS,u'effect'),
		(PRESENTATIONNS,u'delay'),
		(PRESENTATIONNS,u'start-scale'),
		(PRESENTATIONNS,u'path-id'),
		(PRESENTATIONNS,u'speed'),
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'show-text'):(
		(PRESENTATIONNS,u'direction'),
		(PRESENTATIONNS,u'effect'),
		(PRESENTATIONNS,u'delay'),
		(PRESENTATIONNS,u'start-scale'),
		(PRESENTATIONNS,u'path-id'),
		(PRESENTATIONNS,u'speed'),
		(DRAWNS,u'shape-id'),
	),
	(PRESENTATIONNS,u'sound'):(
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(PRESENTATIONNS,u'play-full'),
		(XLINKNS,u'show'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(SCRIPTNS,u'event-listener'):(
		(SCRIPTNS,u'language'),
		(SCRIPTNS,u'macro-name'),
		(XLINKNS,u'actuate'),
		(SCRIPTNS,u'event-name'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(STYLENS,u'background-image'):(
		(DRAWNS,u'opacity'),
		(STYLENS,u'repeat'),
		(XLINKNS,u'show'),
		(XLINKNS,u'actuate'),
		(STYLENS,u'filter-name'),
		(XLINKNS,u'href'),
		(STYLENS,u'position'),
		(XLINKNS,u'type'),
	),
# allowed_attributes
	(STYLENS,u'chart-properties'): (
		(CHARTNS,u'angle-offset'),
		(CHARTNS,u'auto-position'),
		(CHARTNS,u'auto-size'),
		(CHARTNS,u'axis-label-position'),
		(CHARTNS,u'axis-position'),
		(CHARTNS,u'connect-bars'),
		(CHARTNS,u'data-label-number'),
		(CHARTNS,u'data-label-symbol'),
		(CHARTNS,u'data-label-text'),
		(CHARTNS,u'deep'),
		(CHARTNS,u'display-label'),
		(CHARTNS,u'error-category'),
		(CHARTNS,u'error-lower-indicator'),
		(CHARTNS,u'error-lower-limit'),
		(CHARTNS,u'error-lower-range'),
		(CHARTNS,u'error-margin'),
		(CHARTNS,u'error-percentage'),
		(CHARTNS,u'error-upper-indicator'),
		(CHARTNS,u'error-upper-limit'),
		(CHARTNS,u'error-upper-range'),
		(CHARTNS,u'gap-width'),
		(CHARTNS,u'group-bars-per-axis'),
		(CHARTNS,u'hole-size'),
		(CHARTNS,u'include-hidden-cells'),
		(CHARTNS,u'interpolation'),
		(CHARTNS,u'interval-major'),
		(CHARTNS,u'interval-minor-divisor'),
		(CHARTNS,u'japanese-candle-stick'),
		(CHARTNS,u'label-arrangement'),
		(CHARTNS,u'label-position'),
		(CHARTNS,u'label-position-negative'),
		(CHARTNS,u'lines'),
		(CHARTNS,u'link-data-style-to-source'),
		(CHARTNS,u'logarithmic'),
		(CHARTNS,u'maximum'),
		(CHARTNS,u'mean-value'),
		(CHARTNS,u'minimum'),
		(CHARTNS,u'origin'),
		(CHARTNS,u'overlap'),
		(CHARTNS,u'percentage'),
		(CHARTNS,u'pie-offset'),
		(CHARTNS,u'regression-type'),
		(CHARTNS,u'reverse-direction'),
		(CHARTNS,u'right-angled-axes'),
		(CHARTNS,u'scale-text'),
		(CHARTNS,u'series-source'),
		(CHARTNS,u'solid-type'),
		(CHARTNS,u'sort-by-x-values'),
		(CHARTNS,u'spline-order'),
		(CHARTNS,u'spline-resolution'),
		(CHARTNS,u'stacked'),
		(CHARTNS,u'symbol-height'),
		(CHARTNS,u'symbol-name'),
		(CHARTNS,u'symbol-type'),
		(CHARTNS,u'symbol-width'),
		(CHARTNS,u'text-overlap'),
		(CHARTNS,u'three-dimensional'),
		(CHARTNS,u'tick-mark-position'),
		(CHARTNS,u'tick-marks-major-inner'),
		(CHARTNS,u'tick-marks-major-outer'),
		(CHARTNS,u'tick-marks-minor-inner'),
		(CHARTNS,u'tick-marks-minor-outer'),
		(CHARTNS,u'treat-empty-cells'),
		(CHARTNS,u'vertical'),
		(CHARTNS,u'visible'),
		(STYLENS,u'direction'),
		(STYLENS,u'rotation-angle'),
		(TEXTNS,u'line-break'),
	),
	(STYLENS,u'column'):(
		(FONS,u'end-indent'),
		(FONS,u'space-before'),
		(FONS,u'start-indent'),
		(FONS,u'space-after'),
		(STYLENS,u'rel-width'),
	),
	(STYLENS,u'column-sep'):(
		(STYLENS,u'color'),
		(STYLENS,u'width'),
		(STYLENS,u'style'),
		(STYLENS,u'vertical-align'),
		(STYLENS,u'height'),
	),
	(STYLENS,u'columns'):(
		(FONS,u'column-count'),
		(FONS,u'column-gap'),
	),
# allowed_attributes
	(STYLENS,u'default-page-layout'):(
	),
	(STYLENS,u'default-style'):(
		(STYLENS,u'family'),
	),
# allowed_attributes
	(STYLENS,u'drawing-page-properties'): (
		(DRAWNS,u'fill'),
		(DRAWNS,u'fill-color'),
		(DRAWNS,u'secondary-fill-color'),
		(DRAWNS,u'fill-gradient-name'),
		(DRAWNS,u'gradient-step-count'),
		(DRAWNS,u'fill-hatch-name'),
		(DRAWNS,u'fill-hatch-solid'),
		(DRAWNS,u'fill-image-name'),
		(STYLENS,u'repeat'),
		(DRAWNS,u'fill-image-width'),
		(DRAWNS,u'fill-image-height'),
		(DRAWNS,u'fill-image-ref-point-x'),
		(DRAWNS,u'fill-image-ref-point-y'),
		(DRAWNS,u'fill-image-ref-point'),
		(DRAWNS,u'tile-repeat-offset'),
		(DRAWNS,u'opacity'),
		(DRAWNS,u'opacity-name'),
		(SVGNS,u'fill-rule'),
		(PRESENTATIONNS,u'transition-type'),
		(PRESENTATIONNS,u'transition-style'),
		(PRESENTATIONNS,u'transition-speed'),
		(SMILNS,u'type'),
		(SMILNS,u'subtype'),
		(SMILNS,u'direction'),
		(SMILNS,u'fadeColor'),
		(PRESENTATIONNS,u'duration'),
		(PRESENTATIONNS,u'visibility'),
		(DRAWNS,u'background-size'),
		(PRESENTATIONNS,u'background-objects-visible'),
		(PRESENTATIONNS,u'background-visible'),
		(PRESENTATIONNS,u'display-header'),
		(PRESENTATIONNS,u'display-footer'),
		(PRESENTATIONNS,u'display-page-number'),
		(PRESENTATIONNS,u'display-date-time'),
	),
	(STYLENS,u'drop-cap'):(
		(STYLENS,u'distance'),
		(STYLENS,u'length'),
		(STYLENS,u'style-name'),
		(STYLENS,u'lines'),
	),
# allowed_attributes
	(STYLENS,u'font-face'):(
		(STYLENS,u'font-adornments'),
		(STYLENS,u'font-charset'),
		(STYLENS,u'font-family-generic'),
		(STYLENS,u'font-pitch'),
		(STYLENS,u'name'),
		(SVGNS,u'accent-height'),
		(SVGNS,u'alphabetic'),
		(SVGNS,u'ascent'),
		(SVGNS,u'bbox'),
		(SVGNS,u'cap-height'),
		(SVGNS,u'descent'),
		(SVGNS,u'font-family'),
		(SVGNS,u'font-size'),
		(SVGNS,u'font-stretch'),
		(SVGNS,u'font-style'),
		(SVGNS,u'font-variant'),
		(SVGNS,u'font-weight'),
		(SVGNS,u'hanging'),
		(SVGNS,u'ideographic'),
		(SVGNS,u'mathematical'),
		(SVGNS,u'overline-position'),
		(SVGNS,u'overline-thickness'),
		(SVGNS,u'panose-1'),
		(SVGNS,u'slope'),
		(SVGNS,u'stemh'),
		(SVGNS,u'stemv'),
		(SVGNS,u'strikethrough-position'),
		(SVGNS,u'strikethrough-thickness'),
		(SVGNS,u'underline-position'),
		(SVGNS,u'underline-thickness'),
		(SVGNS,u'unicode-range'),
		(SVGNS,u'units-per-em'),
		(SVGNS,u'v-alphabetic'),
		(SVGNS,u'v-hanging'),
		(SVGNS,u'v-ideographic'),
		(SVGNS,u'v-mathematical'),
		(SVGNS,u'widths'),
		(SVGNS,u'x-height'),
	),
	(STYLENS,u'footer'):(
		(STYLENS,u'display'),
	),
	(STYLENS,u'footer-left'):(
		(STYLENS,u'display'),
	),
	(STYLENS,u'footer-style'):(
	),
	(STYLENS,u'footnote-sep'):(
		(STYLENS,u'distance-after-sep'),
		(STYLENS,u'color'),
		(STYLENS,u'rel-width'),
		(STYLENS,u'width'),
		(STYLENS,u'distance-before-sep'),
		(STYLENS,u'line-style'),
		(STYLENS,u'adjustment'),
	),
# allowed_attributes
	(STYLENS,u'graphic-properties'): (
		(DR3DNS,u'ambient-color'),
		(DR3DNS,u'back-scale'),
		(DR3DNS,u'backface-culling'),
		(DR3DNS,u'close-back'),
		(DR3DNS,u'close-front'),
		(DR3DNS,u'depth'),
		(DR3DNS,u'diffuse-color'),
		(DR3DNS,u'edge-rounding'),
		(DR3DNS,u'edge-rounding-mode'),
		(DR3DNS,u'emissive-color'),
		(DR3DNS,u'end-angle'),
		(DR3DNS,u'horizontal-segments'),
		(DR3DNS,u'lighting-mode'),
		(DR3DNS,u'normals-direction'),
		(DR3DNS,u'normals-kind'),
		(DR3DNS,u'shadow'),
		(DR3DNS,u'shininess'),
		(DR3DNS,u'specular-color'),
		(DR3DNS,u'texture-filter'),
		(DR3DNS,u'texture-generation-mode-x'),
		(DR3DNS,u'texture-generation-mode-y'),
		(DR3DNS,u'texture-kind'),
		(DR3DNS,u'texture-mode'),
		(DR3DNS,u'vertical-segments'),
		(DRAWNS,u'auto-grow-height'),
		(DRAWNS,u'auto-grow-width'),
		(DRAWNS,u'blue'),
		(DRAWNS,u'caption-angle'),
		(DRAWNS,u'caption-angle-type'),
		(DRAWNS,u'caption-escape'),
		(DRAWNS,u'caption-escape-direction'),
		(DRAWNS,u'caption-fit-line-length'),
		(DRAWNS,u'caption-gap'),
		(DRAWNS,u'caption-line-length'),
		(DRAWNS,u'caption-type'),
		(DRAWNS,u'color-inversion'),
		(DRAWNS,u'color-mode'),
		(DRAWNS,u'contrast'),
		(DRAWNS,u'decimal-places'),
		(DRAWNS,u'draw-aspect'),
		(DRAWNS,u'end-guide'),
		(DRAWNS,u'end-line-spacing-horizontal'),
		(DRAWNS,u'end-line-spacing-vertical'),
		(DRAWNS,u'fill'),
		(DRAWNS,u'fill-color'),
		(DRAWNS,u'fill-gradient-name'),
		(DRAWNS,u'fill-hatch-name'),
		(DRAWNS,u'fill-hatch-solid'),
		(DRAWNS,u'fill-image-height'),
		(DRAWNS,u'fill-image-name'),
		(DRAWNS,u'fill-image-ref-point'),
		(DRAWNS,u'fill-image-ref-point-x'),
		(DRAWNS,u'fill-image-ref-point-y'),
		(DRAWNS,u'fill-image-width'),
# allowed_attributes
		(DRAWNS,u'fit-to-contour'),
		(DRAWNS,u'fit-to-size'),
		(DRAWNS,u'frame-display-border'),
		(DRAWNS,u'frame-display-scrollbar'),
		(DRAWNS,u'frame-margin-horizontal'),
		(DRAWNS,u'frame-margin-vertical'),
		(DRAWNS,u'gamma'),
		(DRAWNS,u'gradient-step-count'),
		(DRAWNS,u'green'),
		(DRAWNS,u'guide-distance'),
		(DRAWNS,u'guide-overhang'),
		(DRAWNS,u'image-opacity'),
		(DRAWNS,u'line-distance'),
		(DRAWNS,u'luminance'),
		(DRAWNS,u'marker-end'),
		(DRAWNS,u'marker-end-center'),
		(DRAWNS,u'marker-end-width'),
		(DRAWNS,u'marker-start'),
		(DRAWNS,u'marker-start-center'),
		(DRAWNS,u'marker-start-width'),
		(DRAWNS,u'measure-align'),
		(DRAWNS,u'measure-vertical-align'),
		(DRAWNS,u'ole-draw-aspect'),
		(DRAWNS,u'opacity'),
		(DRAWNS,u'opacity-name'),
		(DRAWNS,u'parallel'),
		(DRAWNS,u'placing'),
		(DRAWNS,u'red'),
		(DRAWNS,u'secondary-fill-color'),
		(DRAWNS,u'shadow'),
		(DRAWNS,u'shadow-color'),
		(DRAWNS,u'shadow-offset-x'),
		(DRAWNS,u'shadow-offset-y'),
		(DRAWNS,u'shadow-opacity'),
		(DRAWNS,u'show-unit'),
		(DRAWNS,u'start-guide'),
		(DRAWNS,u'start-line-spacing-horizontal'),
		(DRAWNS,u'start-line-spacing-vertical'),
		(DRAWNS,u'stroke'),
		(DRAWNS,u'stroke-dash'),
		(DRAWNS,u'stroke-dash-names'),
		(DRAWNS,u'stroke-linejoin'),
		(DRAWNS,u'symbol-color'),
		(DRAWNS,u'textarea-horizontal-align'),
		(DRAWNS,u'textarea-vertical-align'),
		(DRAWNS,u'tile-repeat-offset'),
		(DRAWNS,u'unit'),
		(DRAWNS,u'visible-area-height'),
		(DRAWNS,u'visible-area-left'),
		(DRAWNS,u'visible-area-top'),
		(DRAWNS,u'visible-area-width'),
		(DRAWNS,u'wrap-influence-on-position'),
# allowed_attributes
		(FONS,u'background-color'),
		(FONS,u'border'),
		(FONS,u'border-bottom'),
		(FONS,u'border-left'),
		(FONS,u'border-right'),
		(FONS,u'border-top'),
		(FONS,u'clip'),
		(FONS,u'margin'),
		(FONS,u'margin-bottom'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(FONS,u'margin-top'),
		(FONS,u'max-height'),
		(FONS,u'max-width'),
		(FONS,u'min-height'),
		(FONS,u'min-width'),
		(FONS,u'padding'),
		(FONS,u'padding-bottom'),
		(FONS,u'padding-left'),
		(FONS,u'padding-right'),
		(FONS,u'padding-top'),
		(FONS,u'wrap-option'),
		(STYLENS,u'background-transparency'),
		(STYLENS,u'border-line-width'),
		(STYLENS,u'border-line-width-bottom'),
		(STYLENS,u'border-line-width-left'),
		(STYLENS,u'border-line-width-right'),
		(STYLENS,u'border-line-width-top'),
		(STYLENS,u'editable'),
		(STYLENS,u'flow-with-text'),
		(STYLENS,u'horizontal-pos'),
		(STYLENS,u'horizontal-rel'),
		(STYLENS,u'mirror'),
		(STYLENS,u'number-wrapped-paragraphs'),
		(STYLENS,u'overflow-behavior'),
		(STYLENS,u'print-content'),
		(STYLENS,u'protect'),
		(STYLENS,u'rel-height'),
		(STYLENS,u'rel-width'),
		(STYLENS,u'repeat'),
		(STYLENS,u'run-through'),
		(STYLENS,u'shadow'),
		(STYLENS,u'shrink-to-fit'),
		(STYLENS,u'vertical-pos'),
		(STYLENS,u'vertical-rel'),
		(STYLENS,u'wrap'),
		(STYLENS,u'wrap-contour'),
		(STYLENS,u'wrap-contour-mode'),
		(STYLENS,u'wrap-dynamic-threshold'),
		(STYLENS,u'writing-mode'),
		(SVGNS,u'fill-rule'),
		(SVGNS,u'height'),
		(SVGNS,u'stroke-color'),
		(SVGNS,u'stroke-linecap'),
		(SVGNS,u'stroke-opacity'),
		(SVGNS,u'stroke-width'),
		(SVGNS,u'width'),
		(SVGNS,u'x'),
		(SVGNS,u'y'),
		(TEXTNS,u'anchor-page-number'),
		(TEXTNS,u'anchor-type'),
		(TEXTNS,u'animation'),
		(TEXTNS,u'animation-delay'),
		(TEXTNS,u'animation-direction'),
		(TEXTNS,u'animation-repeat'),
		(TEXTNS,u'animation-start-inside'),
		(TEXTNS,u'animation-steps'),
		(TEXTNS,u'animation-stop-inside'),
	),
	(STYLENS,u'handout-master'):(
		(PRESENTATIONNS,u'presentation-page-layout-name'),
		(STYLENS,u'page-layout-name'),
		(PRESENTATIONNS,u'use-footer-name'),
		(DRAWNS,u'style-name'),
		(PRESENTATIONNS,u'use-header-name'),
		(PRESENTATIONNS,u'use-date-time-name'),
	),
# allowed_attributes
	(STYLENS,u'header'):(
		(STYLENS,u'display'),
	),
	(STYLENS,u'header-footer-properties'): (
		(FONS,u'background-color'),
		(FONS,u'border'),
		(FONS,u'border-bottom'),
		(FONS,u'border-left'),
		(FONS,u'border-right'),
		(FONS,u'border-top'),
		(FONS,u'margin'),
		(FONS,u'margin-bottom'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(FONS,u'margin-top'),
		(FONS,u'min-height'),
		(FONS,u'padding'),
		(FONS,u'padding-bottom'),
		(FONS,u'padding-left'),
		(FONS,u'padding-right'),
		(FONS,u'padding-top'),
		(STYLENS,u'border-line-width'),
		(STYLENS,u'border-line-width-bottom'),
		(STYLENS,u'border-line-width-left'),
		(STYLENS,u'border-line-width-right'),
		(STYLENS,u'border-line-width-top'),
		(STYLENS,u'dynamic-spacing'),
		(STYLENS,u'shadow'),
		(SVGNS,u'height'),
	),
	(STYLENS,u'header-left'):(
		(STYLENS,u'display'),
	),
	(STYLENS,u'header-style'):(
	),
	(STYLENS,u'list-level-label-alignment'):(
		(FONS,u'text-indent'),
		(TEXTNS,u'label-followed-by'),
		(TEXTNS,u'list-tab-stop-position'),
		(FONS,u'margin-left'),
	),
# allowed_attributes
	(STYLENS,u'list-level-properties'): (
		(FONS,u'height'),
		(FONS,u'text-align'),
		(FONS,u'width'),
		(STYLENS,u'font-name'),
		(STYLENS,u'vertical-pos'),
		(STYLENS,u'vertical-rel'),
		(SVGNS,u'y'),
		(TEXTNS,u'list-level-position-and-space-mode'),
		(TEXTNS,u'min-label-distance'),
		(TEXTNS,u'min-label-width'),
		(TEXTNS,u'space-before'),
	),
	(STYLENS,u'map'):(
		(STYLENS,u'apply-style-name'),
		(STYLENS,u'base-cell-address'),
		(STYLENS,u'condition'),
	),
	(STYLENS,u'master-page'):(
		(STYLENS,u'page-layout-name'),
		(STYLENS,u'display-name'),
		(DRAWNS,u'style-name'),
		(STYLENS,u'name'),
		(STYLENS,u'next-style-name'),
	),
	(STYLENS,u'page-layout'):(
		(STYLENS,u'name'),
		(STYLENS,u'page-usage'),
	),
# allowed_attributes
	(STYLENS,u'page-layout-properties'): (
		(FONS,u'background-color'),
		(FONS,u'border'),
		(FONS,u'border-bottom'),
		(FONS,u'border-left'),
		(FONS,u'border-right'),
		(FONS,u'border-top'),
		(FONS,u'margin'),
		(FONS,u'margin-bottom'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(FONS,u'margin-top'),
		(FONS,u'padding'),
		(FONS,u'padding-bottom'),
		(FONS,u'padding-left'),
		(FONS,u'padding-right'),
		(FONS,u'padding-top'),
		(FONS,u'page-height'),
		(FONS,u'page-width'),
		(STYLENS,u'border-line-width'),
		(STYLENS,u'border-line-width-bottom'),
		(STYLENS,u'border-line-width-left'),
		(STYLENS,u'border-line-width-right'),
		(STYLENS,u'border-line-width-top'),
		(STYLENS,u'first-page-number'),
		(STYLENS,u'footnote-max-height'),
		(STYLENS,u'layout-grid-base-height'),
		(STYLENS,u'layout-grid-base-width'),
		(STYLENS,u'layout-grid-color'),
		(STYLENS,u'layout-grid-display'),
		(STYLENS,u'layout-grid-lines'),
		(STYLENS,u'layout-grid-mode'),
		(STYLENS,u'layout-grid-print'),
		(STYLENS,u'layout-grid-ruby-below'),
		(STYLENS,u'layout-grid-ruby-height'),
		(STYLENS,u'layout-grid-snap-to'),
		(STYLENS,u'layout-grid-standard-mode'),
		(STYLENS,u'num-format'),
		(STYLENS,u'num-letter-sync'),
		(STYLENS,u'num-prefix'),
		(STYLENS,u'num-suffix'),
		(STYLENS,u'paper-tray-name'),
		(STYLENS,u'print'),
		(STYLENS,u'print-orientation'),
		(STYLENS,u'print-page-order'),
		(STYLENS,u'register-truth-ref-style-name'),
		(STYLENS,u'scale-to'),
		(STYLENS,u'scale-to-pages'),
		(STYLENS,u'shadow'),
		(STYLENS,u'table-centering'),
		(STYLENS,u'writing-mode'),
                (LOEXTNS,u'scale-to-X'),
                (LOEXTNS,u'scale-to-Y')
	),
# allowed_attributes
	(STYLENS,u'paragraph-properties'): (
		(FONS,u'background-color'),
		(FONS,u'border'),
		(FONS,u'border-bottom'),
		(FONS,u'border-left'),
		(FONS,u'border-right'),
		(FONS,u'border-top'),
		(FONS,u'break-after'),
		(FONS,u'break-before'),
		(FONS,u'hyphenation-keep'),
		(FONS,u'hyphenation-ladder-count'),
		(FONS,u'keep-together'),
		(FONS,u'keep-with-next'),
		(FONS,u'line-height'),
		(FONS,u'margin'),
		(FONS,u'margin-bottom'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(FONS,u'margin-top'),
		(FONS,u'orphans'),
		(FONS,u'padding'),
		(FONS,u'padding-bottom'),
		(FONS,u'padding-left'),
		(FONS,u'padding-right'),
		(FONS,u'padding-top'),
		(FONS,u'text-align'),
		(FONS,u'text-align-last'),
		(FONS,u'text-indent'),
		(FONS,u'widows'),
		(LOEXTNS,u'contextual-spacing'),
		(STYLENS,u'auto-text-indent'),
		(STYLENS,u'background-transparency'),
		(STYLENS,u'border-line-width'),
		(STYLENS,u'border-line-width-bottom'),
		(STYLENS,u'border-line-width-left'),
		(STYLENS,u'border-line-width-right'),
		(STYLENS,u'border-line-width-top'),
		(STYLENS,u'font-independent-line-spacing'),
		(STYLENS,u'join-border'),
		(STYLENS,u'justify-single-word'),
		(STYLENS,u'line-break'),
		(STYLENS,u'line-height-at-least'),
		(STYLENS,u'line-spacing'),
		(STYLENS,u'page-number'),
		(STYLENS,u'punctuation-wrap'),
		(STYLENS,u'register-true'),
		(STYLENS,u'shadow'),
		(STYLENS,u'snap-to-layout-grid'),
		(STYLENS,u'tab-stop-distance'),
		(STYLENS,u'text-autospace'),
		(STYLENS,u'vertical-align'),
		(STYLENS,u'writing-mode'),
		(STYLENS,u'writing-mode-automatic'),
		(TEXTNS,u'line-number'),
		(TEXTNS,u'number-lines'),
	),
	(STYLENS,u'presentation-page-layout'):(
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
	),
# allowed_attributes
	(STYLENS,u'region-center'):(
	),
	(STYLENS,u'region-left'):(
	),
	(STYLENS,u'region-right'):(
	),
	(STYLENS,u'ruby-properties'): (
		(STYLENS,u'ruby-position'),
		(STYLENS,u'ruby-align'),
	),
	(STYLENS,u'section-properties'): (
		(FONS,u'background-color'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(STYLENS,u'editable'),
		(STYLENS,u'protect'),
		(STYLENS,u'writing-mode'),
		(TEXTNS,u'dont-balance-text-columns'),
	),
	(STYLENS,u'style'):(
		(STYLENS,u'auto-update'),
		(STYLENS,u'class'),
		(STYLENS,u'data-style-name'),
		(STYLENS,u'default-outline-level'),
		(STYLENS,u'display-name'),
		(STYLENS,u'family'),
		(STYLENS,u'list-level'),
		(STYLENS,u'list-style-name'),
		(STYLENS,u'master-page-name'),
		(STYLENS,u'name'),
		(STYLENS,u'next-style-name'),
		(STYLENS,u'parent-style-name'),
		(STYLENS,u'percentage-data-style-name'),
	),
# allowed_attributes
	(STYLENS,u'tab-stop'):(
		(STYLENS,u'leader-text-style'),
		(STYLENS,u'leader-width'),
		(STYLENS,u'leader-style'),
		(STYLENS,u'char'),
		(STYLENS,u'leader-color'),
		(STYLENS,u'position'),
		(STYLENS,u'leader-text'),
		(STYLENS,u'type'),
		(STYLENS,u'leader-type'),
	),
	(STYLENS,u'tab-stops'):(
	),
	(STYLENS,u'table-cell-properties'): (
		(FONS,u'background-color'),
		(FONS,u'border'),
		(FONS,u'border-bottom'),
		(FONS,u'border-left'),
		(FONS,u'border-right'),
		(FONS,u'border-top'),
		(FONS,u'padding'),
		(FONS,u'padding-bottom'),
		(FONS,u'padding-left'),
		(FONS,u'padding-right'),
		(FONS,u'padding-top'),
		(FONS,u'wrap-option'),
		(STYLENS,u'border-line-width'),
		(STYLENS,u'border-line-width-bottom'),
		(STYLENS,u'border-line-width-left'),
		(STYLENS,u'border-line-width-right'),
		(STYLENS,u'border-line-width-top'),
		(STYLENS,u'cell-protect'),
		(STYLENS,u'decimal-places'),
		(STYLENS,u'diagonal-bl-tr'),
		(STYLENS,u'diagonal-bl-tr-widths'),
		(STYLENS,u'diagonal-tl-br'),
		(STYLENS,u'diagonal-tl-br-widths'),
		(STYLENS,u'direction'),
		(STYLENS,u'glyph-orientation-vertical'),
		(STYLENS,u'print-content'),
		(STYLENS,u'repeat-content'),
		(STYLENS,u'rotation-align'),
		(STYLENS,u'rotation-angle'),
		(STYLENS,u'shadow'),
		(STYLENS,u'shrink-to-fit'),
		(STYLENS,u'text-align-source'),
		(STYLENS,u'vertical-align'),
		(STYLENS,u'writing-mode'),
	),
# allowed_attributes
	(STYLENS,u'table-column-properties'): (
		(FONS,u'break-after'),
		(FONS,u'break-before'),
		(STYLENS,u'column-width'),
		(STYLENS,u'rel-column-width'),
		(STYLENS,u'use-optimal-column-width'),
	),
	(STYLENS,u'table-properties'): (
		(FONS,u'background-color'),
		(FONS,u'break-after'),
		(FONS,u'break-before'),
		(FONS,u'keep-with-next'),
		(FONS,u'margin'),
		(FONS,u'margin-bottom'),
		(FONS,u'margin-left'),
		(FONS,u'margin-right'),
		(FONS,u'margin-top'),
		(STYLENS,u'may-break-between-rows'),
		(STYLENS,u'page-number'),
		(STYLENS,u'rel-width'),
		(STYLENS,u'shadow'),
		(STYLENS,u'width'),
		(STYLENS,u'writing-mode'),
		(TABLENS,u'align'),
		(TABLENS,u'border-model'),
		(TABLENS,u'display'),
	),
	(STYLENS,u'table-row-properties'): (
		(FONS,u'background-color'),
		(FONS,u'break-after'),
		(FONS,u'break-before'),
		(FONS,u'keep-together'),
		(STYLENS,u'min-row-height'),
		(STYLENS,u'row-height'),
		(STYLENS,u'use-optimal-row-height'),
	),
# allowed_attributes
	(STYLENS,u'text-properties'): (
		(FONS,u'background-color'),
		(FONS,u'color'),
		(FONS,u'country'),
		(FONS,u'font-family'),
		(FONS,u'font-size'),
		(FONS,u'font-style'),
		(FONS,u'font-variant'),
		(FONS,u'font-weight'),
		(FONS,u'hyphenate'),
		(FONS,u'hyphenation-push-char-count'),
		(FONS,u'hyphenation-remain-char-count'),
		(FONS,u'language'),
		(FONS,u'letter-spacing'),
		(FONS,u'script'),
		(FONS,u'text-shadow'),
		(FONS,u'text-transform'),
		(STYLENS,u'country-asian'),
		(STYLENS,u'country-complex'),
		(STYLENS,u'font-charset'),
		(STYLENS,u'font-charset-asian'),
		(STYLENS,u'font-charset-complex'),
		(STYLENS,u'font-family-asian'),
		(STYLENS,u'font-family-complex'),
		(STYLENS,u'font-family-generic'),
		(STYLENS,u'font-family-generic-asian'),
		(STYLENS,u'font-family-generic-complex'),
		(STYLENS,u'font-name'),
		(STYLENS,u'font-name-asian'),
		(STYLENS,u'font-name-complex'),
		(STYLENS,u'font-pitch'),
		(STYLENS,u'font-pitch-asian'),
		(STYLENS,u'font-pitch-complex'),
		(STYLENS,u'font-relief'),
		(STYLENS,u'font-size-asian'),
		(STYLENS,u'font-size-complex'),
		(STYLENS,u'font-size-rel'),
		(STYLENS,u'font-size-rel-asian'),
		(STYLENS,u'font-size-rel-complex'),
		(STYLENS,u'font-style-asian'),
		(STYLENS,u'font-style-complex'),
		(STYLENS,u'font-style-name'),
		(STYLENS,u'font-style-name-asian'),
		(STYLENS,u'font-style-name-complex'),
		(STYLENS,u'font-weight-asian'),
		(STYLENS,u'font-weight-complex'),
		(STYLENS,u'language-asian'),
		(STYLENS,u'language-complex'),
		(STYLENS,u'letter-kerning'),
		(STYLENS,u'rfc-language-tag'),
		(STYLENS,u'rfc-language-tag-asian'),
		(STYLENS,u'rfc-language-tag-complex'),
		(STYLENS,u'script-asian'),
		(STYLENS,u'script-complex'),
		(STYLENS,u'script-type'),
		(STYLENS,u'text-blinking'),
		(STYLENS,u'text-combine'),
		(STYLENS,u'text-combine-end-char'),
		(STYLENS,u'text-combine-start-char'),
		(STYLENS,u'text-emphasize'),
		(STYLENS,u'text-line-through-color'),
		(STYLENS,u'text-line-through-mode'),
		(STYLENS,u'text-line-through-style'),
		(STYLENS,u'text-line-through-text'),
		(STYLENS,u'text-line-through-text-style'),
		(STYLENS,u'text-line-through-type'),
		(STYLENS,u'text-line-through-width'),
		(STYLENS,u'text-outline'),
		(STYLENS,u'text-overline-color'),
		(STYLENS,u'text-overline-mode'),
		(STYLENS,u'text-overline-style'),
		(STYLENS,u'text-overline-type'),
		(STYLENS,u'text-overline-width'),
		(STYLENS,u'text-position'),
		(STYLENS,u'text-rotation-angle'),
		(STYLENS,u'text-rotation-scale'),
		(STYLENS,u'text-scale'),
		(STYLENS,u'text-underline-color'),
		(STYLENS,u'text-underline-mode'),
		(STYLENS,u'text-underline-style'),
		(STYLENS,u'text-underline-type'),
		(STYLENS,u'text-underline-width'),
		(STYLENS,u'use-window-font-color'),
		(TEXTNS,u'condition'),
		(TEXTNS,u'display'),
	),
	(SVGNS,u'definition-src'):(
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(SVGNS,u'desc'):(
	),
	(SVGNS,u'font-face-format'):(
		(SVGNS,u'string'),
	),
# allowed_attributes
	(SVGNS,u'font-face-name'):(
		(SVGNS,u'name'),
	),
	(SVGNS,u'font-face-src'):(
	),
	(SVGNS,u'font-face-uri'):(
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(SVGNS,u'linearGradient'):(
		(SVGNS,u'y2'),
		(DRAWNS,u'name'),
		(SVGNS,u'spreadMethod'),
		(SVGNS,u'gradientUnits'),
		(SVGNS,u'x2'),
		(SVGNS,u'gradientTransform'),
		(SVGNS,u'y1'),
		(DRAWNS,u'display-name'),
		(SVGNS,u'x1'),
	),
	(SVGNS,u'radialGradient'):(
		(DRAWNS,u'name'),
		(SVGNS,u'fx'),
		(SVGNS,u'fy'),
		(SVGNS,u'spreadMethod'),
		(SVGNS,u'gradientUnits'),
		(SVGNS,u'cy'),
		(SVGNS,u'cx'),
		(SVGNS,u'gradientTransform'),
		(DRAWNS,u'display-name'),
		(SVGNS,u'r'),
	),
	(SVGNS,u'stop'):(
		(SVGNS,u'stop-color'),
		(SVGNS,u'stop-opacity'),
		(SVGNS,u'offset'),
	),
	(SVGNS,u'title'):(
	),
# allowed_attributes
	(TABLENS,u'background'):(
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'body'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'calculation-settings'):(
		(TABLENS,u'automatic-find-labels'),
		(TABLENS,u'case-sensitive'),
		(TABLENS,u'search-criteria-must-apply-to-whole-cell'),
		(TABLENS,u'precision-as-shown'),
		(TABLENS,u'use-regular-expressions'),
		(TABLENS,u'use-wildcards'),
		(TABLENS,u'null-year'),
	),
	(TABLENS,u'cell-address'):(
		(TABLENS,u'column'),
		(TABLENS,u'table'),
		(TABLENS,u'row'),
	),
	(TABLENS,u'cell-content-change'):(
		(TABLENS,u'id'),
		(TABLENS,u'rejecting-change-id'),
		(TABLENS,u'acceptance-state'),
	),
# allowed_attributes
	(TABLENS,u'cell-content-deletion'):(
		(TABLENS,u'id'),
	),
	(TABLENS,u'cell-range-source'):(
		(TABLENS,u'last-row-spanned'),
		(TABLENS,u'last-column-spanned'),
		(TABLENS,u'name'),
		(TABLENS,u'filter-options'),
		(XLINKNS,u'actuate'),
		(TABLENS,u'filter-name'),
		(XLINKNS,u'href'),
		(TABLENS,u'refresh-delay'),
		(XLINKNS,u'type'),
	),
	(TABLENS,u'change-deletion'):(
		(TABLENS,u'id'),
	),
	(TABLENS,u'change-track-table-cell'):(
		(OFFICENS,u'string-value'),
		(TABLENS,u'cell-address'),
		(TABLENS,u'number-matrix-columns-spanned'),
		(TABLENS,u'number-matrix-rows-spanned'),
		(TABLENS,u'matrix-covered'),
		(OFFICENS,u'value-type'),
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(OFFICENS,u'value'),
		(TABLENS,u'formula'),
		(OFFICENS,u'time-value'),
	),
	(TABLENS,u'consolidation'):(
		(TABLENS,u'function'),
		(TABLENS,u'source-cell-range-addresses'),
		(TABLENS,u'target-cell-address'),
		(TABLENS,u'link-to-source-data'),
		(TABLENS,u'use-labels'),
	),
	(TABLENS,u'content-validation'):(
		(TABLENS,u'base-cell-address'),
		(TABLENS,u'display-list'),
		(TABLENS,u'allow-empty-cell'),
		(TABLENS,u'name'),
		(TABLENS,u'condition'),
	),
	(TABLENS,u'content-validations'):(
	),
# allowed_attributes
	(TABLENS,u'covered-table-cell'):(
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'time-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'value-type'),
		(TABLENS,u'content-validation-name'),
		(TABLENS,u'formula'),
		(TABLENS,u'number-columns-repeated'),
		(TABLENS,u'protect'),
		(TABLENS,u'protected'),
		(TABLENS,u'style-name'),
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XHTMLNS,u'datatype'),
		(XHTMLNS,u'property'),
		(XMLNS,u'id'),
	),
	(TABLENS,u'cut-offs'):(
	),
	(TABLENS,u'data-pilot-display-info'):(
		(TABLENS,u'member-count'),
		(TABLENS,u'data-field'),
		(TABLENS,u'enabled'),
		(TABLENS,u'display-member-mode'),
	),
	(TABLENS,u'data-pilot-field'):(
		(TABLENS,u'selected-page'),
		(TABLENS,u'function'),
		(TABLENS,u'orientation'),
		(TABLENS,u'used-hierarchy'),
		(TABLENS,u'is-data-layout-field'),
		(TABLENS,u'source-field-name'),
	),
	(TABLENS,u'data-pilot-field-reference'):(
		(TABLENS,u'member-name'),
		(TABLENS,u'field-name'),
		(TABLENS,u'member-type'),
		(TABLENS,u'type'),
	),
# allowed_attributes
	(TABLENS,u'data-pilot-group'):(
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-group-member'):(
		(TABLENS,u'name'),
	),
	(TABLENS,u'data-pilot-groups'):(
		(TABLENS,u'date-end'),
		(TABLENS,u'end'),
		(TABLENS,u'start'),
		(TABLENS,u'source-field-name'),
		(TABLENS,u'step'),
		(TABLENS,u'date-start'),
		(TABLENS,u'grouped-by'),
	),
	(TABLENS,u'data-pilot-layout-info'):(
		(TABLENS,u'add-empty-lines'),
		(TABLENS,u'layout-mode'),
	),
	(TABLENS,u'data-pilot-level'):(
		(TABLENS,u'show-empty'),
	),
# allowed_attributes
	(TABLENS,u'data-pilot-member'):(
		(TABLENS,u'show-details'),
		(TABLENS,u'name'),
		(TABLENS,u'display'),
	),
	(TABLENS,u'data-pilot-members'):(
	),
	(TABLENS,u'data-pilot-sort-info'):(
		(TABLENS,u'data-field'),
		(TABLENS,u'sort-mode'),
		(TABLENS,u'order'),
	),
	(TABLENS,u'data-pilot-subtotal'):(
		(TABLENS,u'function'),
	),
	(TABLENS,u'data-pilot-subtotals'):(
	),
	(TABLENS,u'data-pilot-table'):(
		(TABLENS,u'buttons'),
		(TABLENS,u'application-data'),
		(TABLENS,u'name'),
		(TABLENS,u'drill-down-on-double-click'),
		(TABLENS,u'target-range-address'),
		(TABLENS,u'ignore-empty-rows'),
		(TABLENS,u'identify-categories'),
		(TABLENS,u'show-filter-button'),
		(TABLENS,u'grand-total'),
	),
# allowed_attributes
	(TABLENS,u'data-pilot-tables'):(
	),
	(TABLENS,u'database-range'):(
		(TABLENS,u'orientation'),
		(TABLENS,u'target-range-address'),
		(TABLENS,u'contains-header'),
		(TABLENS,u'on-update-keep-size'),
		(TABLENS,u'name'),
		(TABLENS,u'is-selection'),
		(TABLENS,u'refresh-delay'),
		(TABLENS,u'display-filter-buttons'),
		(TABLENS,u'has-persistent-data'),
		(TABLENS,u'on-update-keep-styles'),
	),
	(TABLENS,u'database-ranges'):(
	),
	(TABLENS,u'database-source-query'):(
		(TABLENS,u'query-name'),
		(TABLENS,u'database-name'),
	),
# allowed_attributes
	(TABLENS,u'database-source-sql'):(
		(TABLENS,u'parse-sql-statement'),
		(TABLENS,u'database-name'),
		(TABLENS,u'sql-statement'),
	),
	(TABLENS,u'database-source-table'):(
		(TABLENS,u'database-table-name'),
		(TABLENS,u'database-name'),
	),
	(TABLENS,u'dde-link'):(
	),
	(TABLENS,u'dde-links'):(
	),
	(TABLENS,u'deletion'):(
		(TABLENS,u'rejecting-change-id'),
		(TABLENS,u'multi-deletion-spanned'),
		(TABLENS,u'acceptance-state'),
		(TABLENS,u'table'),
		(TABLENS,u'position'),
		(TABLENS,u'type'),
		(TABLENS,u'id'),
	),
# allowed_attributes
	(TABLENS,u'deletions'):(
	),
	(TABLENS,u'dependencies'):(
	),
	(TABLENS,u'dependency'):(
		(TABLENS,u'id'),
	),
	(TABLENS,u'desc'):(
	),
	(TABLENS,u'detective'):(
	),
	(TABLENS,u'error-macro'):(
		(TABLENS,u'execute'),
	),
	(TABLENS,u'error-message'):(
		(TABLENS,u'display'),
		(TABLENS,u'message-type'),
		(TABLENS,u'title'),
	),
	(TABLENS,u'even-columns'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'even-rows'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
# allowed_attributes
	(TABLENS,u'filter'):(
		(TABLENS,u'target-range-address'),
		(TABLENS,u'display-duplicates'),
		(TABLENS,u'condition-source-range-address'),
		(TABLENS,u'condition-source'),
	),
	(TABLENS,u'filter-and'):(
	),
	(TABLENS,u'filter-condition'):(
		(TABLENS,u'operator'),
		(TABLENS,u'field-number'),
		(TABLENS,u'data-type'),
		(TABLENS,u'case-sensitive'),
		(TABLENS,u'value'),
	),
	(TABLENS,u'filter-or'):(
	),
# allowed_attributes
	(TABLENS,u'filter-set-item'):(
		(TABLENS,u'value'),
	),
	(TABLENS,u'first-column'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'first-row'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
# allowed_attributes
	(TABLENS,u'help-message'):(
		(TABLENS,u'display'),
		(TABLENS,u'title'),
	),
	(TABLENS,u'highlighted-range'):(
		(TABLENS,u'contains-error'),
		(TABLENS,u'direction'),
		(TABLENS,u'marked-invalid'),
		(TABLENS,u'cell-range-address'),
	),
	(TABLENS,u'insertion'):(
		(TABLENS,u'count'),
		(TABLENS,u'rejecting-change-id'),
		(TABLENS,u'acceptance-state'),
		(TABLENS,u'table'),
		(TABLENS,u'position'),
		(TABLENS,u'type'),
		(TABLENS,u'id'),
	),
	(TABLENS,u'insertion-cut-off'):(
		(TABLENS,u'position'),
		(TABLENS,u'id'),
	),
	(TABLENS,u'iteration'):(
		(TABLENS,u'status'),
		(TABLENS,u'maximum-difference'),
		(TABLENS,u'steps'),
	),
# allowed_attributes
	(TABLENS,u'label-range'):(
		(TABLENS,u'label-cell-range-address'),
		(TABLENS,u'data-cell-range-address'),
		(TABLENS,u'orientation'),
	),
	(TABLENS,u'label-ranges'):(
	),
	(TABLENS,u'last-column'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'last-row'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'movement'):(
		(TABLENS,u'id'),
		(TABLENS,u'rejecting-change-id'),
		(TABLENS,u'acceptance-state'),
	),
	(TABLENS,u'movement-cut-off'):(
		(TABLENS,u'position'),
		(TABLENS,u'end-position'),
		(TABLENS,u'start-position'),
	),
	(TABLENS,u'named-expression'):(
		(TABLENS,u'base-cell-address'),
		(TABLENS,u'expression'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'named-expressions'):(
	),
	(TABLENS,u'named-range'):(
		(TABLENS,u'range-usable-as'),
		(TABLENS,u'base-cell-address'),
		(TABLENS,u'name'),
		(TABLENS,u'cell-range-address'),
	),
	(TABLENS,u'null-date'):(
		(TABLENS,u'date-value'),
		(TABLENS,u'value-type'),
	),
	(TABLENS,u'odd-columns'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'odd-rows'):(
		(TABLENS,u'paragraph-style-name'),
		(TABLENS,u'style-name'),
	),
	(TABLENS,u'operation'):(
		(TABLENS,u'index'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'previous'):(
		(TABLENS,u'id'),
	),
	(TABLENS,u'scenario'):(
		(TABLENS,u'comment'),
		(TABLENS,u'border-color'),
		(TABLENS,u'copy-back'),
		(TABLENS,u'is-active'),
		(TABLENS,u'protected'),
		(TABLENS,u'copy-formulas'),
		(TABLENS,u'copy-styles'),
		(TABLENS,u'scenario-ranges'),
		(TABLENS,u'display-border'),
	),
	(TABLENS,u'shapes'):(
	),
# allowed_attributes
	(TABLENS,u'sort'):(
		(TABLENS,u'case-sensitive'),
		(TABLENS,u'embedded-number-behavior'),
		(TABLENS,u'algorithm'),
		(TABLENS,u'target-range-address'),
		(TABLENS,u'country'),
		(TABLENS,u'language'),
		(TABLENS,u'bind-styles-to-content'),
		(TABLENS,u'rfc-language-tag'),
		(TABLENS,u'script'),
	),
	(TABLENS,u'sort-by'):(
		(TABLENS,u'field-number'),
		(TABLENS,u'data-type'),
		(TABLENS,u'order'),
	),
	(TABLENS,u'sort-groups'):(
		(TABLENS,u'data-type'),
		(TABLENS,u'order'),
	),
	(TABLENS,u'source-cell-range'):(
		(TABLENS,u'cell-range-address'),
	),
	(TABLENS,u'source-range-address'):(
		(TABLENS,u'column'),
		(TABLENS,u'end-column'),
		(TABLENS,u'start-table'),
		(TABLENS,u'end-row'),
		(TABLENS,u'table'),
		(TABLENS,u'start-row'),
		(TABLENS,u'row'),
		(TABLENS,u'end-table'),
		(TABLENS,u'start-column'),
	),
# allowed_attributes
	(TABLENS,u'source-service'):(
		(TABLENS,u'user-name'),
		(TABLENS,u'source-name'),
		(TABLENS,u'password'),
		(TABLENS,u'object-name'),
		(TABLENS,u'name'),
	),
	(TABLENS,u'subtotal-field'):(
		(TABLENS,u'function'),
		(TABLENS,u'field-number'),
	),
	(TABLENS,u'subtotal-rule'):(
		(TABLENS,u'group-by-field-number'),
	),
	(TABLENS,u'subtotal-rules'):(
		(TABLENS,u'bind-styles-to-content'),
		(TABLENS,u'page-breaks-on-group-change'),
		(TABLENS,u'case-sensitive'),
	),
	(TABLENS,u'table'):(
		(TABLENS,u'is-sub-table'),
		(TABLENS,u'name'),
		(TABLENS,u'print'),
		(TABLENS,u'print-ranges'),
		(TABLENS,u'protected'),
		(TABLENS,u'protection-key'),
		(TABLENS,u'protection-key-digest-algorithm'),
		(TABLENS,u'style-name'),
		(TABLENS,u'template-name'),
		(TABLENS,u'use-banding-columns-styles'),
		(TABLENS,u'use-banding-rows-styles'),
		(TABLENS,u'use-first-column-styles'),
		(TABLENS,u'use-first-row-styles'),
		(TABLENS,u'use-last-column-styles'),
		(TABLENS,u'use-last-row-styles'),
		(XMLNS,u'id'),
	),
	(TABLENS,u'table-cell'):(
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'time-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'value-type'),
		(TABLENS,u'content-validation-name'),
		(TABLENS,u'formula'),
		(TABLENS,u'number-columns-repeated'),
		(TABLENS,u'number-columns-spanned'),
		(TABLENS,u'number-matrix-columns-spanned'),
		(TABLENS,u'number-matrix-rows-spanned'),
		(TABLENS,u'number-rows-spanned'),
		(TABLENS,u'protect'),
		(TABLENS,u'protected'),
		(TABLENS,u'style-name'),
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XHTMLNS,u'datatype'),
		(XHTMLNS,u'property'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(TABLENS,u'table-column'):(
		(TABLENS,u'style-name'),
		(TABLENS,u'default-cell-style-name'),
		(TABLENS,u'visibility'),
		(TABLENS,u'number-columns-repeated'),
		(XMLNS,u'id'),
	),
	(TABLENS,u'table-column-group'):(
		(TABLENS,u'display'),
	),
	(TABLENS,u'table-columns'):(
	),
	(TABLENS,u'table-header-columns'):(
	),
	(TABLENS,u'table-header-rows'):(
	),
	(TABLENS,u'table-row'):(
		(TABLENS,u'number-rows-repeated'),
		(TABLENS,u'style-name'),
		(TABLENS,u'visibility'),
		(TABLENS,u'default-cell-style-name'),
		(XMLNS,u'id'),
	),
	(TABLENS,u'table-row-group'):(
		(TABLENS,u'display'),
	),
	(TABLENS,u'table-rows'):(
	),
	(TABLENS,u'table-source'):(
		(TABLENS,u'filter-options'),
		(XLINKNS,u'actuate'),
		(TABLENS,u'filter-name'),
		(XLINKNS,u'href'),
		(TABLENS,u'mode'),
		(TABLENS,u'table-name'),
		(XLINKNS,u'type'),
		(TABLENS,u'refresh-delay'),
	),
# allowed_attributes
	(TABLENS,u'table-template'):(
		(TABLENS,u'last-row-end-column'),
		(TABLENS,u'first-row-end-column'),
		(TABLENS,u'name'),
		(TABLENS,u'last-row-start-column'),
		(TABLENS,u'first-row-start-column'),
	),
	(TABLENS,u'target-range-address'):(
		(TABLENS,u'column'),
		(TABLENS,u'end-column'),
		(TABLENS,u'start-table'),
		(TABLENS,u'end-row'),
		(TABLENS,u'table'),
		(TABLENS,u'start-row'),
		(TABLENS,u'row'),
		(TABLENS,u'end-table'),
		(TABLENS,u'start-column'),
	),
	(TABLENS,u'title'):(
	),
	(TABLENS,u'tracked-changes'):(
		(TABLENS,u'track-changes'),
	),
# allowed_attributes
	(TEXTNS,u'a'):(
		(OFFICENS,u'name'),
		(OFFICENS,u'target-frame-name'),
		(OFFICENS,u'title'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'visited-style-name'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'show'),
		(XLINKNS,u'type'),
	),
	(TEXTNS,u'alphabetical-index'):(
		(TEXTNS,u'name'),
		(TEXTNS,u'protected'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(TEXTNS,u'style-name'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'alphabetical-index-auto-mark-file'):(
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
	),
	(TEXTNS,u'alphabetical-index-entry-template'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'alphabetical-index-mark'):(
		(TEXTNS,u'main-entry'),
		(TEXTNS,u'key1-phonetic'),
		(TEXTNS,u'key2'),
		(TEXTNS,u'key1'),
		(TEXTNS,u'string-value'),
		(TEXTNS,u'key2-phonetic'),
		(TEXTNS,u'string-value-phonetic'),
	),
# allowed_attributes
	(TEXTNS,u'alphabetical-index-mark-end'):(
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'alphabetical-index-mark-start'):(
		(TEXTNS,u'main-entry'),
		(TEXTNS,u'key1-phonetic'),
		(TEXTNS,u'key2'),
		(TEXTNS,u'key1'),
		(TEXTNS,u'string-value-phonetic'),
		(TEXTNS,u'key2-phonetic'),
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'alphabetical-index-source'):(
		(FONS,u'country'),
		(FONS,u'language'),
		(FONS,u'script'),
		(STYLENS,u'rfc-language-tag'),
		(TEXTNS,u'alphabetical-separators'),
		(TEXTNS,u'capitalize-entries'),
		(TEXTNS,u'combine-entries'),
		(TEXTNS,u'combine-entries-with-dash'),
		(TEXTNS,u'combine-entries-with-pp'),
		(TEXTNS,u'comma-separated'),
		(TEXTNS,u'ignore-case'),
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'main-entry-style-name'),
		(TEXTNS,u'relative-tab-stop-position'),
		(TEXTNS,u'sort-algorithm'),
		(TEXTNS,u'use-keys-as-entries'),
	),
# allowed_attributes
	(TEXTNS,u'author-initials'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'author-name'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'bibliography'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'bibliography-configuration'):(
		(FONS,u'country'),
		(FONS,u'language'),
		(FONS,u'script'),
		(STYLENS,u'rfc-language-tag'),
		(TEXTNS,u'numbered-entries'),
		(TEXTNS,u'prefix'),
		(TEXTNS,u'sort-algorithm'),
		(TEXTNS,u'sort-by-position'),
		(TEXTNS,u'suffix'),
	),
	(TEXTNS,u'bibliography-entry-template'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'bibliography-type'),
	),
# allowed_attributes
	(TEXTNS,u'bibliography-mark'):(
		(TEXTNS,u'address'),
		(TEXTNS,u'annote'),
		(TEXTNS,u'author'),
		(TEXTNS,u'bibliography-type'),
		(TEXTNS,u'booktitle'),
		(TEXTNS,u'chapter'),
		(TEXTNS,u'custom1'),
		(TEXTNS,u'custom2'),
		(TEXTNS,u'custom3'),
		(TEXTNS,u'custom4'),
		(TEXTNS,u'custom5'),
		(TEXTNS,u'edition'),
		(TEXTNS,u'editor'),
		(TEXTNS,u'howpublished'),
		(TEXTNS,u'identifier'),
		(TEXTNS,u'institution'),
		(TEXTNS,u'isbn'),
		(TEXTNS,u'issn'),
		(TEXTNS,u'journal'),
		(TEXTNS,u'month'),
		(TEXTNS,u'note'),
		(TEXTNS,u'number'),
		(TEXTNS,u'organizations'),
		(TEXTNS,u'pages'),
		(TEXTNS,u'publisher'),
		(TEXTNS,u'report-type'),
		(TEXTNS,u'school'),
		(TEXTNS,u'series'),
		(TEXTNS,u'title'),
		(TEXTNS,u'url'),
		(TEXTNS,u'volume'),
		(TEXTNS,u'year'),
	),
	(TEXTNS,u'bibliography-source'):(
	),
	(TEXTNS,u'bookmark'):(
		(TEXTNS,u'name'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'bookmark-end'):(
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'bookmark-ref'):(
		(TEXTNS,u'ref-name'),
		(TEXTNS,u'reference-format'),
	),
	(TEXTNS,u'bookmark-start'):(
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XHTMLNS,u'datatype'),
		(XHTMLNS,u'property'),
		(TEXTNS,u'name'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(TEXTNS,u'change'):(
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'change-end'):(
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'change-start'):(
		(TEXTNS,u'change-id'),
	),
	(TEXTNS,u'changed-region'):(
		(TEXTNS,u'id'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'chapter'):(
		(TEXTNS,u'display'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'conditional-text'):(
		(TEXTNS,u'string-value-if-true'),
		(TEXTNS,u'current-value'),
		(TEXTNS,u'string-value-if-false'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'creation-date'):(
		(TEXTNS,u'date-value'),
		(TEXTNS,u'fixed'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'creation-time'):(
		(TEXTNS,u'fixed'),
		(TEXTNS,u'time-value'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'creator'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'database-display'):(
		(TEXTNS,u'column-name'),
		(TEXTNS,u'table-name'),
		(TEXTNS,u'table-type'),
		(TEXTNS,u'database-name'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'database-name'):(
		(TEXTNS,u'table-name'),
		(TEXTNS,u'table-type'),
		(TEXTNS,u'database-name'),
	),
	(TEXTNS,u'database-next'):(
		(TEXTNS,u'table-name'),
		(TEXTNS,u'table-type'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'database-row-number'):(
		(STYLENS,u'num-format'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'value'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'table-name'),
		(TEXTNS,u'table-type'),
	),
	(TEXTNS,u'database-row-select'):(
		(TEXTNS,u'row-number'),
		(TEXTNS,u'table-name'),
		(TEXTNS,u'table-type'),
		(TEXTNS,u'database-name'),
		(TEXTNS,u'condition'),
	),
# allowed_attributes
	(TEXTNS,u'date'):(
		(TEXTNS,u'date-value'),
		(TEXTNS,u'fixed'),
		(TEXTNS,u'date-adjust'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'dde-connection'):(
		(TEXTNS,u'connection-name'),
	),
	(TEXTNS,u'dde-connection-decl'):(
		(OFFICENS,u'automatic-update'),
		(OFFICENS,u'dde-topic'),
		(OFFICENS,u'dde-application'),
		(OFFICENS,u'name'),
		(OFFICENS,u'dde-item'),
	),
	(TEXTNS,u'dde-connection-decls'):(
	),
	(TEXTNS,u'deletion'):(
	),
	(TEXTNS,u'description'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'editing-cycles'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'editing-duration'):(
		(TEXTNS,u'duration'),
		(TEXTNS,u'fixed'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'execute-macro'):(
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'expression'):(
		(TEXTNS,u'display'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(STYLENS,u'data-style-name'),
		(OFFICENS,u'value-type'),
		(TEXTNS,u'formula'),
		(OFFICENS,u'time-value'),
	),
	(TEXTNS,u'file-name'):(
		(TEXTNS,u'fixed'),
		(TEXTNS,u'display'),
	),
# allowed_attributes
	(TEXTNS,u'format-change'):(
	),
	(TEXTNS,u'h'):(
		(TEXTNS,u'restart-numbering'),
		(TEXTNS,u'cond-style-name'),
		(TEXTNS,u'is-list-header'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'class-names'),
		(TEXTNS,u'start-value'),
		(TEXTNS,u'id'),
		(TEXTNS,u'outline-level'),
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XHTMLNS,u'datatype'),
		(XHTMLNS,u'property'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'hidden-paragraph'):(
		(TEXTNS,u'is-hidden'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'hidden-text'):(
		(TEXTNS,u'string-value'),
		(TEXTNS,u'is-hidden'),
		(TEXTNS,u'condition'),
	),
	(TEXTNS,u'illustration-index'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'illustration-index-entry-template'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'illustration-index-source'):(
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'caption-sequence-name'),
		(TEXTNS,u'use-caption'),
		(TEXTNS,u'caption-sequence-format'),
		(TEXTNS,u'relative-tab-stop-position'),
	),
	(TEXTNS,u'index-body'):(
	),
	(TEXTNS,u'index-entry-bibliography'):(
		(TEXTNS,u'bibliography-data-field'),
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-chapter'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
		(TEXTNS,u'display'),
	),
# allowed_attributes
	(TEXTNS,u'index-entry-link-end'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-link-start'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-page-number'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-span'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-entry-tab-stop'):(
		(STYLENS,u'position'),
		(TEXTNS,u'style-name'),
		(STYLENS,u'type'),
		(STYLENS,u'leader-char'),
	),
	(TEXTNS,u'index-entry-text'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-source-style'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'index-source-styles'):(
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'index-title'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'index-title-template'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'initial-creator'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'insertion'):(
	),
# allowed_attributes
	(TEXTNS,u'keywords'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'line-break'):(
	),
	(TEXTNS,u'linenumbering-configuration'):(
		(TEXTNS,u'number-position'),
		(TEXTNS,u'number-lines'),
		(STYLENS,u'num-format'),
		(TEXTNS,u'count-empty-lines'),
		(TEXTNS,u'count-in-text-boxes'),
		(TEXTNS,u'style-name'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'increment'),
		(TEXTNS,u'offset'),
		(TEXTNS,u'restart-on-page'),
	),
	(TEXTNS,u'linenumbering-separator'):(
		(TEXTNS,u'increment'),
	),
	(TEXTNS,u'list'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'continue-numbering'),
		(TEXTNS,u'continue-list'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'list-header'):(
		(XMLNS,u'id'),
	),
# allowed_attributes
	(TEXTNS,u'list-item'):(
		(TEXTNS,u'start-value'),
		(TEXTNS,u'style-override'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'list-level-style-bullet'):(
		(TEXTNS,u'level'),
		(STYLENS,u'num-prefix'),
		(STYLENS,u'num-suffix'),
		(TEXTNS,u'bullet-relative-size'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'bullet-char'),
	),
	(TEXTNS,u'list-level-style-image'):(
		(XLINKNS,u'show'),
		(XLINKNS,u'actuate'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(TEXTNS,u'level'),
	),
	(TEXTNS,u'list-level-style-number'):(
		(TEXTNS,u'level'),
		(TEXTNS,u'display-levels'),
		(STYLENS,u'num-format'),
		(STYLENS,u'num-suffix'),
		(TEXTNS,u'style-name'),
		(STYLENS,u'num-prefix'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'start-value'),
	),
# allowed_attributes
	(TEXTNS,u'list-style'):(
		(TEXTNS,u'consecutive-numbering'),
		(STYLENS,u'display-name'),
		(STYLENS,u'name'),
	),
	(TEXTNS,u'measure'):(
		(TEXTNS,u'kind'),
	),
# allowed_attributes
	(TEXTNS,u'meta'):(
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XMLNS,u'id'),
		(XHTMLNS,u'property'),
		(XHTMLNS,u'datatype'),
	),
# allowed_attributes
	(TEXTNS,u'meta-field'):(
		(STYLENS,u'data-style-name'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'modification-date'):(
		(TEXTNS,u'date-value'),
		(TEXTNS,u'fixed'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'modification-time'):(
		(TEXTNS,u'fixed'),
		(TEXTNS,u'time-value'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'note'):(
		(TEXTNS,u'note-class'),
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'note-body'):(
	),
	(TEXTNS,u'note-citation'):(
		(TEXTNS,u'label'),
	),
	(TEXTNS,u'note-continuation-notice-backward'):(
	),
	(TEXTNS,u'note-continuation-notice-forward'):(
	),
	(TEXTNS,u'note-ref'):(
		(TEXTNS,u'ref-name'),
		(TEXTNS,u'note-class'),
		(TEXTNS,u'reference-format'),
	),
	(TEXTNS,u'notes-configuration'):(
		(TEXTNS,u'citation-body-style-name'),
		(STYLENS,u'num-format'),
		(TEXTNS,u'default-style-name'),
		(STYLENS,u'num-suffix'),
		(TEXTNS,u'start-numbering-at'),
		(STYLENS,u'num-prefix'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'citation-style-name'),
		(TEXTNS,u'footnotes-position'),
		(TEXTNS,u'master-page-name'),
		(TEXTNS,u'start-value'),
		(TEXTNS,u'note-class'),
	),
	(TEXTNS,u'number'):(
	),
	(TEXTNS,u'numbered-paragraph'):(
		(TEXTNS,u'continue-numbering'),
		(TEXTNS,u'level'),
		(TEXTNS,u'list-id'),
		(TEXTNS,u'start-value'),
		(TEXTNS,u'style-name'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'object-count'):(
		(STYLENS,u'num-format'),
		(STYLENS,u'num-letter-sync'),
	),
	(TEXTNS,u'object-index'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
# allowed_attributes
	(TEXTNS,u'object-index-entry-template'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'object-index-source'):(
		(TEXTNS,u'use-draw-objects'),
		(TEXTNS,u'use-math-objects'),
		(TEXTNS,u'relative-tab-stop-position'),
		(TEXTNS,u'use-chart-objects'),
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'use-spreadsheet-objects'),
		(TEXTNS,u'use-other-objects'),
	),
	(TEXTNS,u'outline-level-style'):(
		(TEXTNS,u'level'),
		(TEXTNS,u'display-levels'),
		(STYLENS,u'num-format'),
		(STYLENS,u'num-suffix'),
		(TEXTNS,u'style-name'),
		(STYLENS,u'num-prefix'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'start-value'),
	),
	(TEXTNS,u'outline-style'):(
		(STYLENS,u'name'),
	),
# allowed_attributes
	(TEXTNS,u'p'):(
		(TEXTNS,u'class-names'),
		(TEXTNS,u'cond-style-name'),
		(TEXTNS,u'id'),
		(TEXTNS,u'style-name'),
		(XHTMLNS,u'about'),
		(XHTMLNS,u'content'),
		(XHTMLNS,u'datatype'),
		(XHTMLNS,u'property'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'page'):(
		(TEXTNS,u'master-page-name'),
	),
	(TEXTNS,u'page-continuation'):(
		(TEXTNS,u'string-value'),
		(TEXTNS,u'select-page'),
	),
	(TEXTNS,u'page-number'):(
		(TEXTNS,u'page-adjust'),
		(STYLENS,u'num-format'),
		(TEXTNS,u'fixed'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'select-page'),
	),
	(TEXTNS,u'page-sequence'):(
	),
	(TEXTNS,u'page-variable-get'):(
		(STYLENS,u'num-format'),
		(STYLENS,u'num-letter-sync'),
	),
	(TEXTNS,u'page-variable-set'):(
		(TEXTNS,u'active'),
		(TEXTNS,u'page-adjust'),
	),
	(TEXTNS,u'placeholder'):(
		(TEXTNS,u'placeholder-type'),
		(TEXTNS,u'description'),
	),
	(TEXTNS,u'print-date'):(
		(TEXTNS,u'date-value'),
		(TEXTNS,u'fixed'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'print-time'):(
		(TEXTNS,u'fixed'),
		(TEXTNS,u'time-value'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'printed-by'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'reference-mark'):(
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'reference-mark-end'):(
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'reference-mark-start'):(
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'ruby'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'ruby-base'):(
	),
	(TEXTNS,u'ruby-text'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u's'):(
		(TEXTNS,u'c'),
	),
	(TEXTNS,u'script'):(
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(SCRIPTNS,u'language'),
	),
# allowed_attributes
	(TEXTNS,u'section'):(
		(TEXTNS,u'condition'),
		(TEXTNS,u'display'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protected'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(TEXTNS,u'style-name'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'section-source'):(
		(TEXTNS,u'filter-name'),
		(XLINKNS,u'href'),
		(XLINKNS,u'type'),
		(TEXTNS,u'section-name'),
		(XLINKNS,u'show'),
	),
	(TEXTNS,u'sender-city'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-company'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-country'):(
		(TEXTNS,u'fixed'),
	),
# allowed_attributes
	(TEXTNS,u'sender-email'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-fax'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-firstname'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-initials'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-lastname'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-phone-private'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-phone-work'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-position'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-postal-code'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-state-or-province'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-street'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sender-title'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'sequence'):(
		(TEXTNS,u'formula'),
		(STYLENS,u'num-format'),
		(STYLENS,u'num-letter-sync'),
		(TEXTNS,u'name'),
		(TEXTNS,u'ref-name'),
	),
	(TEXTNS,u'sequence-decl'):(
		(TEXTNS,u'separation-character'),
		(TEXTNS,u'display-outline-level'),
		(TEXTNS,u'name'),
	),
	(TEXTNS,u'sequence-decls'):(
	),
	(TEXTNS,u'sequence-ref'):(
		(TEXTNS,u'ref-name'),
		(TEXTNS,u'reference-format'),
	),
	(TEXTNS,u'sheet-name'):(
	),
	(TEXTNS,u'soft-page-break'):(
	),
	(TEXTNS,u'sort-key'):(
		(TEXTNS,u'sort-ascending'),
		(TEXTNS,u'key'),
	),
# allowed_attributes
	(TEXTNS,u'span'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'class-names'),
	),
	(TEXTNS,u'subject'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'tab'):(
		(TEXTNS,u'tab-ref'),
	),
	(TEXTNS,u'table-formula'):(
		(TEXTNS,u'formula'),
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'display'),
	),
	(TEXTNS,u'table-index'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'table-index-entry-template'):(
		(TEXTNS,u'style-name'),
	),
	(TEXTNS,u'table-index-source'):(
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'caption-sequence-name'),
		(TEXTNS,u'use-caption'),
		(TEXTNS,u'caption-sequence-format'),
		(TEXTNS,u'relative-tab-stop-position'),
	),
# allowed_attributes
	(TEXTNS,u'table-of-content'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'table-of-content-entry-template'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'table-of-content-source'):(
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'outline-level'),
		(TEXTNS,u'relative-tab-stop-position'),
		(TEXTNS,u'use-index-marks'),
		(TEXTNS,u'use-outline-level'),
		(TEXTNS,u'use-index-source-styles'),
	),
	(TEXTNS,u'template-name'):(
		(TEXTNS,u'display'),
	),
	(TEXTNS,u'text-input'):(
		(TEXTNS,u'description'),
	),
	(TEXTNS,u'time'):(
		(TEXTNS,u'time-adjust'),
		(TEXTNS,u'fixed'),
		(TEXTNS,u'time-value'),
		(STYLENS,u'data-style-name'),
	),
	(TEXTNS,u'title'):(
		(TEXTNS,u'fixed'),
	),
	(TEXTNS,u'toc-mark'):(
		(TEXTNS,u'string-value'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'toc-mark-end'):(
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'toc-mark-start'):(
		(TEXTNS,u'id'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'tracked-changes'):(
		(TEXTNS,u'track-changes'),
	),
	(TEXTNS,u'user-defined'):(
		(TEXTNS,u'name'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'date-value'),
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'fixed'),
		(OFFICENS,u'time-value'),
	),
	(TEXTNS,u'user-field-decl'):(
		(TEXTNS,u'name'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(OFFICENS,u'value-type'),
		(TEXTNS,u'formula'),
		(OFFICENS,u'time-value'),
	),
	(TEXTNS,u'user-field-decls'):(
	),
	(TEXTNS,u'user-field-get'):(
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'display'),
	),
# allowed_attributes
	(TEXTNS,u'user-field-input'):(
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'description'),
	),
	(TEXTNS,u'user-index'):(
		(TEXTNS,u'protected'),
		(TEXTNS,u'style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'protection-key'),
		(TEXTNS,u'protection-key-digest-algorithm'),
		(XMLNS,u'id'),
	),
	(TEXTNS,u'user-index-entry-template'):(
		(TEXTNS,u'style-name'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'user-index-mark'):(
		(TEXTNS,u'index-name'),
		(TEXTNS,u'string-value'),
		(TEXTNS,u'outline-level'),
	),
	(TEXTNS,u'user-index-mark-end'):(
		(TEXTNS,u'id'),
	),
	(TEXTNS,u'user-index-mark-start'):(
		(TEXTNS,u'index-name'),
		(TEXTNS,u'id'),
		(TEXTNS,u'outline-level'),
	),
# allowed_attributes
	(TEXTNS,u'user-index-source'):(
		(TEXTNS,u'copy-outline-levels'),
		(TEXTNS,u'index-name'),
		(TEXTNS,u'index-scope'),
		(TEXTNS,u'relative-tab-stop-position'),
		(TEXTNS,u'use-floating-frames'),
		(TEXTNS,u'use-graphics'),
		(TEXTNS,u'use-index-marks'),
		(TEXTNS,u'use-index-source-styles'),
		(TEXTNS,u'use-objects'),
		(TEXTNS,u'use-tables'),
	),
	(TEXTNS,u'variable-decl'):(
		(TEXTNS,u'name'),
		(OFFICENS,u'value-type'),
	),
	(TEXTNS,u'variable-decls'):(
	),
	(TEXTNS,u'variable-get'):(
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'name'),
		(TEXTNS,u'display'),
	),
	(TEXTNS,u'variable-input'):(
		(STYLENS,u'data-style-name'),
		(TEXTNS,u'display'),
		(TEXTNS,u'name'),
		(OFFICENS,u'value-type'),
		(TEXTNS,u'description'),
	),
	(TEXTNS,u'variable-set'):(
		(TEXTNS,u'name'),
		(TEXTNS,u'display'),
		(OFFICENS,u'string-value'),
		(OFFICENS,u'value'),
		(OFFICENS,u'boolean-value'),
		(OFFICENS,u'currency'),
		(OFFICENS,u'date-value'),
		(STYLENS,u'data-style-name'),
		(OFFICENS,u'value-type'),
		(TEXTNS,u'formula'),
		(OFFICENS,u'time-value'),
	),
# allowed_attributes
}
