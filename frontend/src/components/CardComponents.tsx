import React from "react"
import { ChartColumn, ChartLine, Download, Zap } from "lucide-react"
import {
  Bar,
  BarChart,
  Cell,
  ComposedChart,
  DefaultTooltipContent,
  LabelList,
  LabelProps,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts"
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent"

import { formatCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  CardContent,
  CardTitle,
  Card as ShadCNCard,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { WrappedTick } from "@/components/ChartComponents"

export const Card = ({
  title,
  flashNumberLabel,
  tabs,
  icons,
  commentary,
  filters,
  isLoading,
  children,
  selectedTab,
  onTabChange,
}: {
  title: string
  flashNumberLabel?: string
  tabs?: string[]
  icons?: React.ReactNode
  commentary?: string
  filters?: React.ReactNode
  isLoading?: boolean
  children: React.ReactNode
  selectedTab?: string
  onTabChange?: (tab: string) => void
}) => (
  <ShadCNCard className="rounded-lg shadow-xs">
    <CardContent className="flex flex-1 flex-col gap-3">
      <div className="flex flex-wrap items-start justify-between gap-3">
        <div className="flex flex-col gap-1.5">
          <CardTitle className="font-medium">{title}</CardTitle>

          {flashNumberLabel !== undefined && (
            <p className="text-muted-foreground flex items-center gap-1 text-xs font-medium">
              <Zap className="inline size-3 text-yellow-500" />
              <span>Flash Number {flashNumberLabel}</span>
            </p>
          )}
        </div>

        {filters && (
          <div className="mr-auto flex items-center gap-2">{filters}</div>
        )}

        <div className="flex items-center gap-2">
          {/* <Button
            size="icon"
            variant="link"
            className="text-foreground hover:text-primary size-3.5"
          >
            <ImageUpscale className="size-3.5" />
          </Button> */}
          {icons}

          <Button
            size="icon"
            variant="link"
            className="text-foreground hover:text-primary size-3.5"
            disabled={isLoading}
          >
            <Download className="size-3.5" />
          </Button>
        </div>
      </div>

      {tabs && (
        <Tabs
          defaultValue={tabs[0]}
          value={selectedTab || tabs[0]}
          onValueChange={(val) => onTabChange?.(val)}
          className="self-end"
        >
          <TabsList>
            {tabs.map((tab) => (
              <TabsTrigger key={tab} value={tab}>
                {tab}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      )}

      <div className="mt-auto">
        {isLoading ? <Skeleton style={{ height: CHART_HEIGHT }} /> : children}
      </div>

      {commentary && (
        <div className="bg-primary/10 border-primary/50 text-primary flex flex-col items-start gap-2 rounded-md border px-4 py-3 text-xs whitespace-pre-wrap">
          <p className="mt-0.5">{commentary}</p>
        </div>
      )}
    </CardContent>
  </ShadCNCard>
)

export const CHART_HEIGHT = 350

type ChartData = {
  year: string
  Queensland: number
  NSW: number
  Victoria: number
  WA: number
  totalRevenue: number
  margin: number
  YOY?: number
}

type ChartDataWithYOYPosition = ChartData & {
  YOYPosition?: number
}

const addYOYPosition = (data: ChartData[]): ChartDataWithYOYPosition[] => {
  const sums: number[] = data.map((entry) => {
    const { Queensland, NSW, Victoria, WA, margin } = entry
    return Queensland + NSW + Victoria + WA + margin
  })

  const maxSum = Math.max(...sums)

  return data.map((entry) => {
    if (!entry.YOY) return entry

    const YOYPosition = maxSum + (entry.YOY / 100) * maxSum + maxSum * 0.15

    return {
      ...entry,
      YOYPosition,
    }
  })
}

export const TemporaryChart1 = () => {
  const chart1Data = addYOYPosition([
    {
      year: "2023",
      Queensland: 58.9,
      NSW: 64.7,
      Victoria: 61.9,
      WA: 58.9,
      totalRevenue: 339.5,
      margin: 20,
      YOY: 10,
    },
    {
      year: "2024",
      Queensland: 17.3,
      NSW: 44.2,
      Victoria: 48.2,
      WA: 48.2,
      totalRevenue: 229.5,
      margin: 10,
      YOY: 15,
    },
    {
      year: "2025B",
      Queensland: 26.2,
      NSW: 79.3,
      Victoria: 36.7,
      WA: 79.3,
      totalRevenue: 149.5,
      margin: 20,
      YOY: 20,
    },
  ])

  const chart2Data = addYOYPosition([
    {
      year: "YTD<br/>2023",
      Queensland: 58.9,
      NSW: 64.7,
      Victoria: 61.9,
      WA: 58.9,
      totalRevenue: 339.5,
      margin: 20,
      YOY: 10,
    },
    {
      year: "YTD<br/>2024",
      Queensland: 17.3,
      NSW: 44.2,
      Victoria: 48.2,
      WA: 48.2,
      totalRevenue: 229.5,
      margin: 10,
      YOY: 15,
    },
    {
      year: "YTD<br/>2025B",
      Queensland: 26.2,
      NSW: 79.3,
      Victoria: 36.7,
      WA: 79.3,
      totalRevenue: 149.5,
      margin: 10,
      YOY: 20,
    },
    {
      year: "YTD<br/>2025",
      Queensland: 26.2,
      NSW: 79.3,
      Victoria: 36.7,
      WA: 79.3,
      totalRevenue: 149.5,
      margin: 10,
    },
  ])

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-2">
        {[chart1Data, "|", chart2Data].map((data, index) =>
          typeof data === "string" ? (
            <Separator
              key={index}
              orientation="vertical"
              style={{
                height: 326,
              }}
            />
          ) : (
            <ResponsiveContainer
              key={index}
              width="99%"
              height={326}
              style={{
                flex: data.length,
              }}
            >
              <ComposedChart data={data} margin={{ top: 12 }}>
                <XAxis
                  dataKey="year"
                  tick={WrappedTick}
                  tickLine={false}
                  interval={0}
                />

                <YAxis orientation="left" hide />

                <Tooltip
                  wrapperStyle={{ zIndex: 1 }}
                  content={<TemporaryChart1CustomTooltip />}
                  formatter={(value: number, name: string) => {
                    if (name === "YoY") return [`${value}%`, "YoY"]
                    return [`${formatCurrency(value, 1)}M`, name]
                  }}
                />

                <Bar
                  dataKey="Queensland"
                  name="Queensland"
                  stackId="a"
                  fill="var(--color-chart-1)"
                >
                  <LabelList
                    dataKey="Queensland"
                    position="center"
                    fill="white"
                    formatter={(value: number) => formatCurrency(value, 1)}
                    fontSize={12}
                  />
                </Bar>

                <Bar
                  dataKey="NSW"
                  name="New South Wales"
                  stackId="a"
                  fill="var(--color-chart-2)"
                >
                  <LabelList
                    dataKey="NSW"
                    position="center"
                    fill="white"
                    formatter={(value: number) => formatCurrency(value, 1)}
                    fontSize={12}
                  />
                </Bar>

                <Bar
                  dataKey="Victoria"
                  name="Victoria"
                  stackId="a"
                  fill="var(--color-chart-3)"
                >
                  <LabelList
                    dataKey="Victoria"
                    position="center"
                    fill="white"
                    formatter={(value: number) => formatCurrency(value, 1)}
                    fontSize={12}
                  />
                </Bar>

                <Bar
                  dataKey="WA"
                  name="Western Australia"
                  stackId="a"
                  fill="var(--color-chart-4)"
                >
                  <LabelList
                    dataKey="WA"
                    position="center"
                    fill="white"
                    formatter={(value: number) => formatCurrency(value, 1)}
                    fontSize={12}
                  />
                  <LabelList
                    dataKey="year"
                    content={(props: LabelProps) => {
                      const { x, y, width, value } = props
                      const item = data.find((d) => d.year === value)
                      if (!item) return null

                      return (
                        <g>
                          <text
                            className="text-xs"
                            x={Number(x) + Number(width) / 2}
                            y={Number(y) - 24}
                            textAnchor="middle"
                            dominantBaseline="middle"
                          >
                            ${formatCurrency(item.totalRevenue, 1)}M
                          </text>
                          <text
                            className="text-xs"
                            x={Number(x) + Number(width) / 2}
                            y={Number(y) - 10}
                            textAnchor="middle"
                            dominantBaseline="middle"
                          >
                            {item.margin}% Margin
                          </text>
                        </g>
                      )
                    }}
                  />
                </Bar>

                <Line
                  dataKey="YOYPosition"
                  name="YoY"
                  stroke="var(--color-chart-5)"
                >
                  <LabelList
                    dataKey="YOY"
                    position="top"
                    fill="var(--color-chart-5)"
                    formatter={(value: number) =>
                      `${formatCurrency(value, 0)}%`
                    }
                    fontSize={12}
                  />
                </Line>
              </ComposedChart>
            </ResponsiveContainer>
          )
        )}
      </div>

      <div className="flex flex-wrap items-center justify-center gap-x-2 gap-y-1 text-xs">
        <div className="text-chart-5 flex items-center gap-2">
          <div className="relative size-2">
            <span className="bg-chart-5 absolute inset-0 rounded-full" />
            <span className="bg-chart-5 absolute top-[3px] -right-1 -left-1 h-0.5" />
          </div>
          YoY Growth
        </div>
        <div className="text-chart-1 flex items-center gap-1">
          <span className="bg-chart-1 size-2 rounded-full" />
          Queensland
        </div>
        <div className="text-chart-2 flex items-center gap-1">
          <span className="bg-chart-2 size-2 rounded-full" />
          New South Wales
        </div>
        <div className="text-chart-3 flex items-center gap-1">
          <span className="bg-chart-3 size-2 rounded-full" />
          Victoria
        </div>
        <div className="text-chart-4 flex items-center gap-1">
          <span className="bg-chart-4 size-2 rounded-full" />
          Western Australia
        </div>
      </div>
    </div>
  )
}

export const TemporaryChart1CustomTooltip = (
  props: TooltipProps<ValueType, NameType>
) => {
  if (!props.active) return null

  const label = String(props.label).replace("<br/>", " ")

  const payload = (props.payload ?? []).map((item) => {
    if (item.name === "YoY") {
      return {
        ...item,
        value: item.payload.YOY,
      }
    }
    return item
  })

  return <DefaultTooltipContent {...props} label={label} payload={payload} />
}

export const TemporaryChart2 = () => {
  const data = [
    {
      period: "2023",
      bankDebt: 16500,
      netDebt: 15500,
    },
    {
      period: "2024",
      bankDebt: 15500,
      netDebt: 14500,
    },
    {
      period: "YTD 2025",
      bankDebt: 18500,
      netDebt: 11500,
    },
  ]

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <BarChart data={data}>
        <XAxis dataKey="period" tick={{ fontSize: 12 }} tickLine={false} />

        <Tooltip />
        <Legend
          iconType="circle"
          iconSize={8}
          verticalAlign="bottom"
          wrapperStyle={{ fontSize: 12 }}
        />

        <Bar dataKey="bankDebt" name="Bank Debt" fill="var(--chart-1)">
          <Cell fill="var(--chart-1)" />

          <LabelList
            dataKey="bankDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatCurrency(value, 0)}
            fontSize={12}
          />
        </Bar>

        <Bar dataKey="netDebt" name="Net Debt" fill="var(--chart-3)">
          <Cell fill="var(--chart-3)" />

          <LabelList
            dataKey="netDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatCurrency(value, 0)}
            fontSize={12}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

export const TemporaryChart3 = ({
  data,
}: {
  data: {
    period: string
    amount: number
    percentage?: number
  }[]
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <BarChart data={data} margin={{ top: 12 }}>
      <XAxis
        dataKey="period"
        tick={WrappedTick}
        tickLine={false}
        interval={0}
      />

      <Tooltip
        content={<TemporaryChart1CustomTooltip />}
        formatter={(value: number) => `${formatCurrency(value, 1)}M`}
      />

      <Bar dataKey="amount" name="Amount" fill="var(--chart-1)">
        <Cell fill="var(--chart-1)" />

        <LabelList
          dataKey="amount"
          position="center"
          fill="white"
          formatter={(value: number) => `${formatCurrency(value, 1)}M`}
          fontSize={12}
        />

        <LabelList
          dataKey="percentage"
          content={(props: LabelProps) => {
            const { x, y, width, value } = props

            if (!value) return null

            return (
              <g>
                <rect
                  className={cn(
                    Number(value) > 0 ? "fill-green-100" : "fill-red-100"
                  )}
                  x={Number(x) + Number(width) / 2 - 25}
                  y={Number(y) - 32}
                  width={50}
                  height={20}
                  rx={12}
                  ry={12}
                />

                <text
                  className={cn(
                    "text-xs font-medium",
                    Number(value) > 0 ? "fill-green-600" : "fill-red-600"
                  )}
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) - 21}
                  textAnchor="middle"
                  dominantBaseline="middle"
                >
                  {Number(value) > 0 ? "+" : ""}
                  {value}%
                </text>
              </g>
            )
          }}
        />
      </Bar>
    </BarChart>
  </ResponsiveContainer>
)

export const TemporarySelect1 = () => (
  <Select value="segment">
    <SelectTrigger size="sm">
      <SelectValue placeholder="Default" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="segment">By Segment</SelectItem>
      <SelectItem value="clinic">By Clinic</SelectItem>
    </SelectContent>
  </Select>
)

export const TemporarySelect2 = () => (
  <Select value="bar">
    <SelectTrigger size="sm">
      <SelectValue placeholder="Default" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="bar">
        <ChartColumn className="size-3.5" />
        Bar
      </SelectItem>
      <SelectItem value="line">
        <ChartLine className="size-3.5" />
        Line
      </SelectItem>
    </SelectContent>
  </Select>
)

export const TemporarySelect3 = () => (
  <Select>
    <SelectTrigger size="sm">
      <SelectValue placeholder="Default" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="segment">By segment</SelectItem>
      <SelectItem value="revenue">+ Revenue</SelectItem>
    </SelectContent>
  </Select>
)
