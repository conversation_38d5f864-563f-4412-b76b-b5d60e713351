import { ChartColumn, ChartLine } from "lucide-react"
import { DefaultTooltipContent, TooltipProps } from "recharts"
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent"
import { TickItem } from "recharts/types/util/types"

import { CHART_TYPES, ChartType } from "@/types/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export const CUSTOM_CHART_COLORS = [
  "#2F3E9E",
  "#2BBBAD",
  "#8E7CC3",
  "#00B86B",
  "#E53935",
  "#FFA000",
  "#B0BEC5",
  "#42A5F5",
  "#E97451",
  "#C6D900",
  "#1E2631",
]

export const ChartTypeSelect = ({
  value,
  setValue,
  disabled,
}: {
  value: ChartType
  setValue: React.Dispatch<React.SetStateAction<ChartType>>
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val as ChartType)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {CHART_TYPES.map((type) => (
        <SelectItem key={type} value={type}>
          {type === "Bar" ? (
            <ChartColumn className="inline size-3.5" />
          ) : (
            <ChartLine className="inline size-3.5" />
          )}
          {type}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

export const CustomSelect = ({
  value,
  setValue,
  options,
  disabled,
}: {
  value: string
  setValue: React.Dispatch<React.SetStateAction<string>>
  options: string[]
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {options.map((option) => (
        <SelectItem key={option} value={option}>
          {option}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

export const WrappedTick = ({
  x,
  y,
  payload,
}: {
  x?: number
  y?: number
  payload: TickItem & { value: ValueType }
}) => {
  const words = payload.value.split("<br/>")

  return (
    <g transform={`translate(${x},${(y || 0) + 10})`}>
      <text textAnchor="middle" fontSize={12} fill="var(--muted-foreground)">
        {words.map((word: string, index: number) => (
          <tspan key={index} x={0} dy={index === 0 ? 0 : 12}>
            {word}
          </tspan>
        ))}
      </text>
    </g>
  )
}

export const WrappedTooltip = (props: TooltipProps<ValueType, NameType>) => {
  if (!props.active) return null

  const label = String(props.label).replace("<br/>", " ")

  const total = props.payload?.[0]?.payload?.total as number | undefined

  const payload = [
    ...(props.payload || []),
    ...(Number.isFinite(total)
      ? [
          {
            name: "Total",
            value: total,
            color: "var(--foreground)",
          },
        ]
      : []),
  ]

  return <DefaultTooltipContent {...props} label={label} payload={payload} />
}
