"use client"

import React from "react"
import {
  Bar,
  Bar<PERSON>hart,
  Cell,
  LabelList,
  ResponsiveContainer,
  XAxis,
} from "recharts"

import { WaterfallChartData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick } from "@/components/ChartComponents"
import { useCreditAndBalanceSheetCurrentCashBalance } from "@/services/summary"

const CurrentCashBalance = () => {
  const { data, isLoading } = useCreditAndBalanceSheetCurrentCashBalance()

  return (
    <Card
      title="Current Cash Balance"
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <WaterfallChart data={data} />
    </Card>
  )
}

export default CurrentCashBalance

export const WaterfallChart = ({ data }: { data: WaterfallChartData[] }) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <BarChart data={data} margin={{ top: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} interval={0} tickLine={false} />

      <Bar dataKey="remaining" stackId="a" fill="transparent" />

      <Bar dataKey="amount" name="Amount" stackId="a" fill="var(--chart-1)">
        {data.map((item, index) => {
          if (item.amount < 0) {
            return <Cell key={index} fill="var(--chart-3)" />
          }

          if (index === data.length - 1) {
            return <Cell key={index} fill="var(--chart-5)" />
          }

          return <Cell key={index} fill="var(--chart-1)" />
        })}

        <LabelList
          dataKey="amount"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Bar>
    </BarChart>
  </ResponsiveContainer>
)
