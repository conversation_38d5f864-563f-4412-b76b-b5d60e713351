"use client"

import React from "react"

import { CHART_TYPES } from "@/types/chart"
import { Card } from "@/components/CardComponents"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useCreditAndBalanceSheetFCF } from "@/services/summary"

const FCF = () => {
  const { data, isLoading } = useCreditAndBalanceSheetFCF()

  return (
    <Card title="FCF" flashNumberLabel="(excl. Vidaskin)" isLoading={isLoading}>
      <RevenueChart data={data} chartType={CHART_TYPES[0]} />
    </Card>
  )
}

export default FCF
