"use client"

import React from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { ChartDataPoint } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick, WrappedTooltip } from "@/components/ChartComponents"
import { useCreditAndBalanceSheetOCF } from "@/services/summary"

const OCF = () => {
  const { data, isLoading } = useCreditAndBalanceSheetOCF()

  return (
    <Card title="OCF" flashNumberLabel="(excl. Vidaskin)" isLoading={isLoading}>
      <OCFChart data={data} />
    </Card>
  )
}

export default OCF

const OCFChart = ({ data }: { data: ChartDataPoint[] }) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} tickLine={false} interval={0} />

      <Tooltip
        content={<WrappedTooltip />}
        formatter={(value: number) => formatAbbreviatedCurrency(value)}
      />

      <Line dataKey="value" name="Amount" stroke="var(--chart-1)">
        <LabelList
          dataKey="value"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Line>
    </LineChart>
  </ResponsiveContainer>
)
