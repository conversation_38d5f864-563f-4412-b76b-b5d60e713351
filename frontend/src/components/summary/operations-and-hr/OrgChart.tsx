"use client"

import React, { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { Expand } from "lucide-react"
import { RenderCustomNodeElementFn } from "react-d3-tree"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { useOperationsAndHROrgChart } from "@/services/summary"

const Tree = dynamic(() => import("react-d3-tree"), { ssr: false })

const LEVEL_COLORS = [
  "oklch(0.28 0.09 268)",
  "oklch(0.38 0.14 266)",
  "oklch(0.42 0.18 266)",
  "oklch(0.49 0.22 264)",
  "oklch(0.6 0.1544 245.96)",
]

const LEGEND_ITEMS = [
  { color: LEVEL_COLORS[0], label: "Organizations" },
  { color: LEVEL_COLORS[1], label: "Clinics" },
  { color: LEVEL_COLORS[2], label: "Departments" },
  { color: LEVEL_COLORS[3], label: "Occupations" },
  { color: LEVEL_COLORS[4], label: "Employees" },
]

const OrgChart = () => {
  const { data, isLoading } = useOperationsAndHROrgChart()

  const chartContainerRef = useRef<HTMLDivElement>(null)
  const [translate, setTranslate] = useState({ x: 0, y: 0 })
  const [dialogOpen, setDialogOpen] = useState(false)
  const [readyToRenderChart, setReadyToRenderChart] = useState(false)

  useEffect(() => {
    if (dialogOpen) {
      setReadyToRenderChart(false)

      requestAnimationFrame(() => {
        if (chartContainerRef.current) {
          const { offsetWidth, offsetHeight } = chartContainerRef.current
          setTranslate({
            x: offsetWidth / 3,
            y: offsetHeight / 2,
          })
          setReadyToRenderChart(true)
        }
      })
    }
  }, [dialogOpen])

  return (
    <Card
      title="Org Chart"
      isLoading={isLoading}
      icons={
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button
              size="icon"
              variant="link"
              className="text-foreground hover:text-primary size-3.5"
            >
              <Expand className="size-3.5" />
            </Button>
          </DialogTrigger>

          <DialogContent className="max-h-[calc(100%-48px)] w-[calc(100%-48px)] max-w-none!">
            <DialogHeader>
              <DialogTitle>Org Chart</DialogTitle>
              <DialogDescription hidden>
                Enlarged view of the org chart
              </DialogDescription>
            </DialogHeader>

            <div className="flex flex-col gap-3">
              <div className="flex flex-wrap justify-center gap-3">
                {LEGEND_ITEMS.map((item, index) => (
                  <div key={index} className="flex items-center gap-1.5">
                    <div
                      className="size-2 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-xs" style={{ color: item.color }}>
                      {item.label}
                    </span>
                  </div>
                ))}
              </div>

              <div
                ref={chartContainerRef}
                className="bg-accent/50 min-h-[80vh] overflow-hidden rounded-md"
              >
                {readyToRenderChart && (
                  <Tree
                    data={data}
                    pathFunc="elbow"
                    translate={translate}
                    zoom={0.75}
                    nodeSize={{ x: 160, y: 80 }}
                    pathClassFunc={() => "stroke-muted-foreground!"}
                    renderCustomNodeElement={renderCustomNodeElement}
                  />
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      }
    >
      <div className="flex flex-col gap-3">
        <div className="flex flex-wrap justify-center gap-3">
          {LEGEND_ITEMS.map((item, index) => (
            <div key={index} className="flex items-center gap-1.5">
              <div
                className="size-2 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-xs" style={{ color: item.color }}>
                {item.label}
              </span>
            </div>
          ))}
        </div>

        <div
          className="bg-accent/50 overflow-hidden rounded-md"
          style={{
            height: CHART_HEIGHT,
          }}
        >
          <Tree
            data={data}
            pathFunc="elbow"
            translate={{
              x: 150,
              y: CHART_HEIGHT / 2,
            }}
            zoom={0.75}
            nodeSize={{ x: 160, y: 80 }}
            pathClassFunc={() => "stroke-muted-foreground!"}
            renderCustomNodeElement={renderCustomNodeElement}
          />
        </div>
      </div>
    </Card>
  )
}

export default OrgChart

const NODE_WIDTH = 140
const NODE_HEIGHT = 70

const renderCustomNodeElement: RenderCustomNodeElementFn = ({
  nodeDatum,
  toggleNode,
}) => {
  const x = -NODE_WIDTH / 2
  const y = -NODE_HEIGHT / 2

  const depth =
    (nodeDatum as { __rd3t?: { depth?: number } }).__rd3t?.depth || 0
  const colorIndex = Math.min(depth, LEVEL_COLORS.length - 1)
  const backgroundColor = LEVEL_COLORS[colorIndex]

  return (
    <foreignObject
      width={NODE_WIDTH}
      height={NODE_HEIGHT}
      x={x}
      y={y}
      onClick={toggleNode}
      style={{ cursor: "pointer" }}
    >
      <div
        className="flex h-full w-full items-center justify-center rounded-md p-3 text-center text-xs font-medium text-white"
        style={{
          backgroundColor,
          wordWrap: "break-word",
        }}
      >
        <span className="leading-tight">{nodeDatum.name}</span>
      </div>
    </foreignObject>
  )
}
